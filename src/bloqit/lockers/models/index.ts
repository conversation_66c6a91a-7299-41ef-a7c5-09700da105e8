import { ActionInitiatedBy } from '../../rents/models/rent';

export enum CodeName {
    activate = 'locker.locker_active',
    deactivate = 'locker.locker_inactive',
}

export interface Metadata {
    bloqExternalID: string;
    lockerExternalID: string;
}

export interface LockerChangeState {
    _id: string;
    bloq: string;
    locker: string;
    codeName: CodeName;
    metadata: Metadata;
}

export interface LockerChangeStateResponse {
    success: boolean;
}

export interface LockerObject {
    _id: string;
    bloq: string;
    rent: string | null;
    lockerTitle: string;
}

export interface LockerObjectResponse {
    status: number;
    lockerObjects: LockerObject[];
}

export interface OpenLockersResponse {
    status: number;
    lockers: string[];
}

export interface OpenLockerWithRentResponse {
    _id: string;
    customer?: string;
    details?: {
        pickUpCode?: string;
    };
    expiryDate?: Date;
    locker?: string;
    startDate?: Date;
    state?: string;
    states?: [
        {
            state: string;
            timestamp: Date;
        },
    ];
    type?: string;
}

export enum LockerType {
    XS = 'XS',
    S = 'S',
    M = 'M',
    L = 'L',
    XL = 'XL',
}

export interface LockerMaintenanceStatusUpdateEvent {
    partner: string;
    title: string;
    code: string;
    description: string;
    codeName: string;
    bloq: string;
    locker: string;
    actionInitiatedBy: ActionInitiatedBy;
    uiFlowContext: string;
    metadata: {
        bloqExternalID: string;
        lockerExternalID: string;
        previousState: string;
        newState: string;
        reason: string;
        country?: string;
    };
}

export interface MappedLocker {
    bloqId: string;
    sharedId: string;
    layout: Array<{
        lockerId: string;
        lockerTitle: string;
        partnerName: string;
        usageType?: LockerUsageType;
        partnerUsageType?: string;
        size: LockerType;
        partnerSize?: string;
        column: number;
        row: number;
    }>;
}

export enum LockerMaintenanceStatus {
    NONE = 'none',
    MAINTENANCE = 'maintenance',
    TRIAGE = 'triage',
    VERIFICATION = 'verification',
}

export enum LockerMaintenanceStatusReason {
    DIRTY = 'dirty_locker',
    DOOR_NOT_OPEN = 'doors_not_open',
    DOOR_NOT_CLOSE = 'doors_not_close',
    DAMAGED = 'damaged',
    COMPARTMENT_DAMAGED = 'compartment_damaged',
    OTHER = 'other',
}

export enum LockerUsageType {
    STORAGE = 'storage',
    ECU = 'ecu',
    BATTERY = 'battery',
}
