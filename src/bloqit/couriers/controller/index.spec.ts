import { CouriersController, type AuthenticateCourierRequest } from '.';
import { FakePartnerRegistry } from '../../../core/partner-registry/fake';
import FakeExpressRequestBuilder from '../../../../test/mocks/http/express/request';
import FakeExpressResponseBuilder from '../../../../test/mocks/http/express/response';
import { simplifyControllerResponse } from '../../../../test/utils';
import { FakePartnerHandler } from '../../../core/partner-registry/fake/handler';
import { BloqCourierPermissions } from '../../bloqs/models/courier-access-codes';

describe('CouriersController', () => {
    const partnerHandler = new FakePartnerHandler();

    afterEach(() => jest.clearAllMocks());

    describe('authenticate courier', () => {
        it('should authenticate a courier by resolving the call to a specific partner handler', async () => {
            const authenticationResult = { success: true, permissions: [] };

            const partnerRegistry = new FakePartnerRegistry();
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'authenticateCourier').mockResolvedValueOnce(
                authenticationResult,
            );

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().withBody({ pin: '123' }).build();

            req.partnerId = 'partner-123';

            const ctrl = new CouriersController({ partnerRegistry });

            const rawResult = await ctrl.authenticateCourier(
                req as unknown as AuthenticateCourierRequest,
                res,
            );
            const result = simplifyControllerResponse(rawResult);

            expect(result.body).toEqual({
                success: true,
                meta: { partnerId: 'partner-123' },
                permissions: [],
            });
        });

        it('should authenticate a courier by resolving the call to a specific partner handler with permissions', async () => {
            const authenticationResult = {
                success: true,
                permissions: [BloqCourierPermissions.DROP_OFF, BloqCourierPermissions.PICK_UP],
            };

            const partnerRegistry = new FakePartnerRegistry();
            jest.spyOn(partnerRegistry, 'get').mockReturnValue(partnerHandler);
            jest.spyOn(partnerHandler, 'authenticateCourier').mockResolvedValueOnce(
                authenticationResult,
            );

            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder().withBody({ pin: '123' }).build();

            req.partnerId = 'partner-123';

            const ctrl = new CouriersController({ partnerRegistry });

            const rawResult = await ctrl.authenticateCourier(
                req as unknown as AuthenticateCourierRequest,
                res,
            );
            const result = simplifyControllerResponse(rawResult);

            expect(result.body).toEqual({
                success: true,
                meta: { partnerId: 'partner-123' },
                permissions: [BloqCourierPermissions.DROP_OFF, BloqCourierPermissions.PICK_UP],
            });
        });
    });
});
