import { type HTTPRequest, type HTTPResponse } from '../../../http/models/extensions/express';
import { type PartnerRegistry } from '../../../core/partner-registry';

export interface AuthenticateCourierRequest extends HTTPRequest {
    body: {
        pin: string;
        bloq: string;
        bloqExternalID: string;
        session: string;
        pwd?: string;
        ID?: string;
        country?: string;
    };
}

export class CouriersController {
    partnerRegistry: PartnerRegistry;

    constructor(deps: { partnerRegistry: PartnerRegistry }) {
        this.partnerRegistry = deps.partnerRegistry;

        this.authenticateCourier = this.authenticateCourier.bind(this);
    }

    async authenticateCourier(
        req: AuthenticateCourierRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        const partnerId = req.partnerId;
        if (partnerId === undefined) throw new Error('Missing partnerId');

        try {
            const partnerHandler = this.partnerRegistry.get(partnerId);

            const {
                pin,
                bloq,
                bloqExternalID,
                session: courierSessionId,
                pwd,
                ID,
                country,
            } = req.body;
            const args = {
                pin,
                bloq,
                bloqExternalId: bloqExternalID,
                courierSessionId,
                password: pwd,
                courierId: ID,
                country,
            };
            const authenticationResult = await partnerHandler?.authenticateCourier(args);

            return res.json({
                success: authenticationResult?.success,
                meta: { partnerId },
                permissions: authenticationResult?.permissions,
            });
        } catch (ex) {
            return res.status(500).json({
                success: false,
                message: (ex as Error).message,
                meta: { partnerId },
                permissions: [],
            });
        }
    }
}
