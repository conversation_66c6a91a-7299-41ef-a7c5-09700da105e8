import { BloqCourierPermissions } from '../../bloqs/models/courier-access-codes';

/**
 * @description Normalized authentication result used across all partner integrations
 * @property {boolean} success - Whether authentication was successful
 * @property {BloqCourierPermissions[]} [permissions] - Array of permissions granted to the courier if authentication was successful
 */
export interface AuthenticationResult {
    success: boolean;
    permissions?: BloqCourierPermissions[];
}
