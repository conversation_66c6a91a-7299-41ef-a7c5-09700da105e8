import { BloqitClientImpl } from '.';
import { FakeHTTPClient } from '../../../core/http-client/fake';
import { FakeLogger } from '../../../core/logger/fake';
import * as httpVerbs from '../../../http/models/verbs';
import * as httpCodes from '../../../http/models/codes';
import { LockerType } from '../../lockers/models';

class MockedAxiosError extends Error {
    private readonly response: { status: number };

    constructor(deps: { message: string; response: { status: number } }) {
        const { message, response } = deps;
        super(message);
        this.response = response;
    }
}

describe('BloqitClientImpl', () => {
    const apiKey = 'api-key';
    const bloqId = 'bloq-id-123';

    const logger = new FakeLogger();
    const httpClient = new FakeHTTPClient();

    afterEach(() => jest.clearAllMocks());

    describe('activateBloq', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to activate a bloq', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: {},
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await client.activateBloq({ bloqId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { active: true },
            });
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.activateBloq({ bloqId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { active: true },
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('deactivateBloq', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to deactivate a bloq', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: {},
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await client.deactivateBloq({ bloqId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { active: false },
            });
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.deactivateBloq({ bloqId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { active: false },
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('getOccupancy', () => {
        it('should fetch data from all lockers in a given bloq', async () => {
            const lockerId1 = 'locker-id-1';
            const lockerId2 = 'locker-id-2';
            const lockerId3 = 'locker-id-2';
            const rentId1 = 'rent-id-1';
            const rentId2 = 'rent-id-2';

            const lockerObjects = [
                { bloq: bloqId, _id: lockerId1, rent: rentId1 },
                { bloq: bloqId, _id: lockerId2, rent: rentId2 },
                { bloq: bloqId, _id: lockerId3 },
            ];

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: { lockers: lockerObjects },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.getOccupancy({ bloqId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(result).toEqual({ status: httpCodes.OK, lockerObjects });
        });

        it('should return the status code and an empty list of locker objects if the request fails', async () => {
            jest.spyOn(httpClient, 'request').mockRejectedValue(
                new MockedAxiosError({
                    message: 'Remote error',
                    response: { status: httpCodes.INTERNAL_SERVER_ERROR },
                }),
            );

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.getOccupancy({ bloqId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(result).toEqual({ status: httpCodes.INTERNAL_SERVER_ERROR, lockerObjects: [] });
        });
    });

    describe('updateBloqMetadata', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        const metadata = { field: 'value' };

        it('should send a request to update the bloq metadata', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: {},
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await client.updateBloqMetadata({ bloqId, metadata });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { metadata },
            });
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.updateBloqMetadata({ bloqId, metadata })).rejects.toThrow(
                'Bloqit Core error',
            );

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { metadata },
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('retrieveBloqByExternalId', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to retrieve a bloq by external ID', async () => {
            const externalId = 'external-id-123';
            const responseData = { id: bloqId, externalId };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.retrieveBloqByExternalId({ externalId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/bloqs/external/${externalId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const externalId = 'external-id-123';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.retrieveBloqByExternalId({ externalId })).rejects.toThrow(
                'Bloqit Core error',
            );

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/bloqs/external/${externalId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('retrieveBloqById', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to retrieve a bloq by ID', async () => {
            const responseData = { id: bloqId };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.retrieveBloqById({ bloqId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.retrieveBloqById({ bloqId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/bloqs/${bloqId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('getRent', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to retrieve a rent by ID', async () => {
            const rentId = 'rent-id-123';
            const responseData = { id: rentId };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.getRent({ rentId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/rents/${rentId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const rentId = 'rent-id-123';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.getRent({ rentId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/rents/${rentId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('openDoors', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to open doors', async () => {
            const bloqId = 'bloq-id';
            const lockers = ['locker-id-1', 'locker-id-2'];
            const responseData = ['locker-id-1', 'locker-id-2'];

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.openDoors({ bloqId, lockers });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}/lockers/open`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { lockers },
            });

            expect(result.lockers).toEqual(responseData);
        });
    });

    describe('openDoorWithRent', () => {
        it('should send a request to open a door with a rent ID', async () => {
            const rentId = 'rent-id-123';
            const responseData = { id: rentId };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.openDoorWithRent({ rentId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/lockers/open`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });
    });

    describe('collectItem', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to collect an item', async () => {
            const rentId = 'rent-id-123';
            const responseData = { id: rentId };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.collectItem({ rentId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/collect`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const rentId = 'rent-id-123';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.collectItem({ rentId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/collect`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('retrieveRentByExternalId', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to retrieve a rent by external ID', async () => {
            const externalId = 'external-id-123';
            const responseData = { id: 'rent-id-123', externalId };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.retrieveRentByExternalId({ externalId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/rents/external/${externalId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const externalId = 'external-id-123';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.retrieveRentByExternalId({ externalId })).rejects.toThrow(
                'Bloqit Core error',
            );

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.GET,
                url: expect.stringContaining(`/rents/external/${externalId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('createRent', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to create a rent', async () => {
            const rent = {
                externalID: 'rent-id',
                pickUpCodes: { pin: '1234' },
                dropOffCode: '5678',
                customer: {
                    email: '<EMAIL>',
                },
                dimensions: {
                    length: 10,
                    width: 5,
                    height: 15,
                },
                prePickupActions: [],
                postPickupAction: [],
                setID: 'set-id',
                priority: 1,
                desiredLockerType: LockerType.M,
            };

            const lockerId = 'locker-id';
            const bloqId = 'bloq-id';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: rent,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.createRent({ rent, lockerId, bloqId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.POST,
                url: expect.stringContaining(`/deliveries`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: rent,
            });

            expect(result).toEqual(rent);
        });

        it('should handle unexpected core status codes', async () => {
            const rent = {
                externalID: 'rent-id',
                pickUpCodes: { pin: '1234' },
                dropOffCode: '5678',
                customer: {
                    email: '<EMAIL>',
                },
                dimensions: {
                    length: 10,
                    width: 5,
                    height: 15,
                },
                prePickupActions: [],
                postPickupAction: [],
                setID: 'set-id',
                priority: 1,
            };
            const lockerId = 'locker-id';
            const bloqId = 'bloq-id';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.createRent({ rent, lockerId, bloqId })).rejects.toThrow(
                'Bloqit Core error',
            );

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.POST,
                url: expect.stringContaining(`/deliveries`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: rent,
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('updateRent', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to update a rent', async () => {
            const rent = {
                externalID: 'rent-id',
                pickUpCodes: { pin: '1234' },
                dropOffCode: '5678',
                customer: {
                    email: '<EMAIL>',
                },
                dimensions: {
                    length: 10,
                    width: 5,
                    height: 15,
                },
                prePickupActions: [],
                postPickupAction: [],
                setID: 'set-id',
                priority: 1,
                desiredLockerType: LockerType.M,
            };

            const rentId = 'rent-id';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: rent,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.updateRent({ rentId, rent });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: rent,
            });

            expect(result).toEqual(rent);
        });

        it('should handle unexpected core status codes', async () => {
            const rent = {
                externalID: 'rent-id',
                pickUpCodes: { pin: '1234' },
                dropOffCode: '5678',
                customer: {
                    email: '<EMAIL>',
                },
                dimensions: {
                    length: 10,
                    width: 5,
                    height: 15,
                },
                prePickupActions: [],
                postPickupAction: [],
                setID: 'set-id',
                priority: 1,
            };
            const rentId = 'rent-id';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.updateRent({ rentId, rent })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: rent,
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('cancelRent', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to cancel a rent by ID', async () => {
            const rentId = 'rent-id-123';
            const responseData = { id: rentId, state: 'cancelled' };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.cancelRent({ rentId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/cancel`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const rentId = 'rent-id-123';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.cancelRent({ rentId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/cancel`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('finishRent', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to finish a rent by ID', async () => {
            const rentId = 'rent-id-123';
            const responseData = { id: rentId, state: 'finished' };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.finishRent({ rentId });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/finish`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const rentId = 'rent-id-123';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(client.finishRent({ rentId })).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/rents/${rentId}/finish`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: '',
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('reportLockerProblem', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to report a locker problem', async () => {
            const bloqId = 'bloq-id-123';
            const lockerId = 'locker-id-123';
            const reason = 'dirty';
            const state = 'maintenance';

            const responseData = { id: lockerId, state };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.reportLockerProblem({ bloqId, lockerId, reason, state });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/lockers/${lockerId}/problem`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { reason, state },
            });

            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const bloqId = 'bloq-id-123';
            const lockerId = 'locker-id-123';
            const reason = 'dirty';
            const state = 'maintenance';

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(
                client.reportLockerProblem({ bloqId, lockerId, reason, state }),
            ).rejects.toThrow('Bloqit Core error');

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(`/bloqs/${bloqId}/lockers/${lockerId}/problem`),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { reason, state },
            });

            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });

    describe('changeLockerAvailability', () => {
        beforeEach(() => {
            jest.spyOn(logger, 'error').mockReturnValue(undefined);
        });

        afterEach(() => jest.clearAllMocks());

        it('should send a request to change locker availability', async () => {
            const bloqId = 'bloq-id-123';
            const lockerId = 'locker-id-123';
            const active = true;
            const responseData = { id: lockerId, active };

            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: responseData,
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            const result = await client.changeLockerAvailability({ bloqId, lockerId, active });

            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(
                    `bloqs/${bloqId}/lockers/${lockerId}/change-availability`,
                ),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { active },
            });
            expect(result).toEqual(responseData);
        });

        it('should handle unexpected Bloqit Core status codes', async () => {
            const bloqId = 'bloq-id-123';
            const lockerId = 'locker-id-123';
            const active = true;
            jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });

            const client = new BloqitClientImpl({ httpClient, apiKey, logger });
            await expect(
                client.changeLockerAvailability({ bloqId, lockerId, active }),
            ).rejects.toThrow('Bloqit Core error');
            expect(httpClient.request).toHaveBeenCalledTimes(1);
            expect(httpClient.request).toHaveBeenCalledWith({
                method: httpVerbs.PUT,
                url: expect.stringContaining(
                    `bloqs/${bloqId}/lockers/${lockerId}/change-availability`,
                ),
                headers: { 'X-API-Key': apiKey, connection: 'keep-alive' },
                data: { active },
            });
            expect(logger.error).toHaveBeenCalledTimes(1);
            expect(logger.error).toHaveBeenCalledWith({
                message: 'Bloqit Core error',
                status: httpCodes.BAD_REQUEST,
                data: { message: 'Bad request' },
            });
        });
    });
});
