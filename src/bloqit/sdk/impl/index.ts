import type { BloqitClient } from '..';
import type { HTTPClient } from '../../../core/http-client';
import type { Logger } from '../../../core/logger';
import * as httpVerbs from '../../../http/models/verbs';
import * as httpCodes from '../../../http/models/codes';
import type {
    LockerObject,
    LockerObjectResponse,
    OpenLockersResponse,
    OpenLockerWithRentResponse,
} from '../../lockers/models';
import type { BloqLocker, BloqObjectResponse } from '../../bloqs/models';
import type {
    CreateRentResponse,
    RentDelivery,
    RentObjectResponse,
    UpdateRentResponse,
} from '../../rents/models/rent';

const env = { BLOQIT_CORE_HOST: process.env.BLOQIT_CORE_HOST ?? '' };

export interface BloqitResponse {
    status: number;
    statusText: string;
    data: { lockers: LockerObject[] };
}

export class BloqitClientImpl implements BloqitClient {
    private readonly httpClient: HTTPClient;
    private readonly partnerAPIKey: string;
    private readonly logger: Logger;

    constructor(props: { httpClient: HTTPClient; apiKey: string; logger: Logger }) {
        this.httpClient = props.httpClient;
        this.partnerAPIKey = props.apiKey;
        this.logger = props.logger;
    }

    async activateBloq(props: { bloqId: string }): Promise<void> {
        await this.toggleBloqActiveState({ bloqId: props.bloqId, value: true });
    }

    async deactivateBloq(props: { bloqId: string }): Promise<void> {
        await this.toggleBloqActiveState({ bloqId: props.bloqId, value: false });
    }

    private async toggleBloqActiveState(props: { bloqId: string; value: boolean }): Promise<void> {
        const { bloqId, value } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: { active: value },
        });

        this.validateResponse(result);
    }

    async getOccupancy(props: { bloqId: string }): Promise<LockerObjectResponse> {
        try {
            const { bloqId } = props;
            const response = await this.httpClient.request<BloqitResponse>({
                method: 'get',
                url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}`,
                headers: { 'x-api-key': this.partnerAPIKey, connection: 'keep-alive' },
                data: '',
            });

            return { status: response.status, lockerObjects: response.data.lockers };
        } catch (error: any) {
            return {
                status: error.response.status ?? httpCodes.INTERNAL_SERVER_ERROR,
                lockerObjects: [],
            };
        }
    }

    async updateBloqMetadata(props: {
        bloqId: string;
        metadata: Record<string, string>;
    }): Promise<void> {
        const { bloqId, metadata } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: { metadata },
        });

        this.validateResponse(result);
    }

    async retrieveBloqById(props: { bloqId: string }): Promise<BloqObjectResponse> {
        const { bloqId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.GET,
            url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async retrieveBloqByExternalId(props: { externalId: string }): Promise<BloqObjectResponse> {
        const { externalId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.GET,
            url: `${env.BLOQIT_CORE_HOST}/bloqs/external/${externalId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async getRent(props: { rentId: string }): Promise<RentObjectResponse> {
        const { rentId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.GET,
            url: `${env.BLOQIT_CORE_HOST}/rents/${rentId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    private validateResponse(response: { status: number; data: any }): void {
        const { status, data } = response;
        if (status !== httpCodes.OK) {
            this.logger.error({ message: 'Bloqit Core error', status, data });
            throw new Error('Bloqit Core error');
        }
    }

    async openDoors(props: { bloqId: string; lockers: string[] }): Promise<OpenLockersResponse> {
        try {
            const { bloqId, lockers } = props;
            const response = await this.httpClient.request<{ status: number; data: any }>({
                method: httpVerbs.PUT,
                url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}/lockers/open`,
                headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
                data: { lockers },
            });

            return {
                status: response.status,
                lockers: response.data,
            };
        } catch (error: any) {
            return {
                status: error.response.status ?? httpCodes.INTERNAL_SERVER_ERROR,
                lockers: [],
            };
        }
    }

    async openDoorWithRent(props: { rentId: string }): Promise<OpenLockerWithRentResponse> {
        const { rentId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/rents/${rentId}/lockers/open`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async createRent(props: {
        rent: RentDelivery;
        lockerId?: string;
        bloqId?: string;
    }): Promise<CreateRentResponse> {
        const { rent, lockerId, bloqId } = props;

        let queryString = '?';

        if (lockerId) {
            const lockerIdString = `lockerID=${lockerId}`;
            queryString += queryString.length > 1 ? `&${lockerIdString}` : lockerIdString;
        }

        if (bloqId) {
            const bloqIdString = `bloqID=${bloqId}`;
            queryString += queryString.length > 1 ? `&${bloqIdString}` : bloqIdString;
        }

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.POST,
            url: `${env.BLOQIT_CORE_HOST}/deliveries/reserveLocker${queryString}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: { ...rent },
        });

        this.validateResponse(result);

        return result.data;
    }

    async updateRent(props: {
        rentId: string;
        rent: Partial<RentDelivery>;
    }): Promise<UpdateRentResponse> {
        const { rentId, rent } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/rents/${rentId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: { ...rent },
        });

        this.validateResponse(result);

        return result.data;
    }

    async collectItem(props: { rentId: string }): Promise<RentObjectResponse> {
        const { rentId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/rents/${rentId}/collect`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async retrieveRentByExternalId(props: { externalId: string }): Promise<RentObjectResponse> {
        const { externalId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.GET,
            url: `${env.BLOQIT_CORE_HOST}/rents/external/${externalId}`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async cancelRent(props: { rentId: string }): Promise<RentObjectResponse> {
        const { rentId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/rents/${rentId}/cancel`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async finishRent(props: { rentId: string }): Promise<RentObjectResponse> {
        const { rentId } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/rents/${rentId}/finish`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: '',
        });

        this.validateResponse(result);

        return result.data;
    }

    async reportLockerProblem(props: {
        bloqId: string;
        lockerId: string;
        reason: string;
        state: string;
    }): Promise<BloqLocker> {
        const { bloqId, lockerId, reason, state } = props;

        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}/lockers/${lockerId}/problem`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: { reason, state },
        });

        this.validateResponse(result);

        return result.data;
    }

    async changeLockerAvailability(props: {
        bloqId: string;
        lockerId: string;
        active: boolean;
    }): Promise<BloqLocker> {
        const { bloqId, lockerId, active } = props;
        const result = await this.httpClient.request<{ status: number; data: any }>({
            method: httpVerbs.PUT,
            url: `${env.BLOQIT_CORE_HOST}/bloqs/${bloqId}/lockers/${lockerId}/change-availability`,
            headers: { 'X-API-Key': this.partnerAPIKey, connection: 'keep-alive' },
            data: { active },
        });

        this.validateResponse(result);

        return result.data;
    }
}
