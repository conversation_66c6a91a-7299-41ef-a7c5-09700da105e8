import {
    type BloqChangeState,
    type BloqChangeStateResponse,
    type BloqEvent,
} from '../../bloqit/bloqs/models';
import { type AuthenticationResult } from '../../bloqit/couriers/models/authentication-result';
import {
    LockerMaintenanceStatusUpdateEvent,
    LockerChangeState,
    LockerChangeStateResponse,
    LockerMaintenanceStatusReason,
    LockerMaintenanceStatus,
} from '../../bloqit/lockers/models';
import {
    type RentEvent,
    type Rent,
    type OperationType,
    type ActionInitiatedBy,
    Roles,
    ActionInitiatedBySources,
    FunctionalityContext as BloqFunctionalityContext,
    UIFlowContext,
} from '../../bloqit/rents/models/rent';
import { AbstractPartnerHandler } from '../../core/partner-registry/handler/abstract';
import { type InPostApiClient } from '../client';
import { type Logger } from '../../core/logger';
import { ActionSource, CountryCode, ParcelOperationType, ParcelStatus } from '../models/enums';
import {
    type ChangeParcelStatusRequest,
    CompartmentState,
    FunctionalityContext,
    type FunctionalityErrorRequest,
    FunctionalityErrorRequestErrorCode,
    type ReportCompartmentStates,
    SessionType,
    LabelAssociation,
} from '../models';
import { type AccessToken } from '../models/access-token';
import { getBoxSizeFromLockerType } from '../common';
import * as crypto from 'crypto';
import {
    CourierAccess,
    CourierAccessCodesResponse,
    HashAlgorithm,
} from '../../bloqit/bloqs/models/courier-access-codes';
import {
    convertToBloqPermissions,
    decodePermissions,
} from '../common/courier-permissions/permission-utils';
import { InPostModelsMapper } from '../models/models-mapper';
import { BloqitClient } from '../../bloqit/sdk';
import { LabelAssociationRepository } from '../data-access/repositories/associate-label/label-association-repository';
import { MappedLockerRepository } from '../data-access/repositories/mapped-locker/mapped-locker-repository';

type LockerMaintenanceStatusTransition = {
    from: LockerMaintenanceStatus;
    to: LockerMaintenanceStatus;
    result: string;
};

type TransitionMap = {
    [key in LockerMaintenanceStatusReason]: LockerMaintenanceStatusTransition[];
};

export class InpostHandlerImpl extends AbstractPartnerHandler {
    private readonly logger: Logger;
    private readonly inpostClient: InPostApiClient;
    private readonly bloqitClient: BloqitClient;
    private accessToken: AccessToken | undefined;
    private labelAssociationRepository: LabelAssociationRepository;
    private mappedLockerRepository: MappedLockerRepository;

    constructor(deps: {
        logger: Logger;
        inpostClient: InPostApiClient;
        bloqitClient: BloqitClient;
        labelAssociationRepository: LabelAssociationRepository;
        mappedLockerRepository: MappedLockerRepository;
    }) {
        super({ partnerFriendlyId: 'inpost' });

        this.logger = deps.logger;
        this.inpostClient = deps.inpostClient;
        this.bloqitClient = deps.bloqitClient;
        this.accessToken = undefined;
        this.labelAssociationRepository = deps.labelAssociationRepository;
        this.mappedLockerRepository = deps.mappedLockerRepository;

        this.getAccessToken = this.getAccessToken.bind(this);
        this.notifyExpiredRents = this.notifyExpiredRents.bind(this);
        this.notifyCollectItem = this.notifyCollectItem.bind(this);
        this.getRentById = this.getRentById.bind(this);
        this.getPrelabeledParcel = this.getPrelabeledParcel.bind(this);
        this.createReverseReturnParcelRent = this.createReverseReturnParcelRent.bind(this);
        this.getRentByQuickSendCode = this.getRentByQuickSendCode.bind(this);
        this.canBePickedUp = this.canBePickedUp.bind(this);
        this.setConfig = this.setConfig.bind(this);
        this.authenticateCourier = this.authenticateCourier.bind(this);
        this.notifyDropOffConfirmation = this.notifyDropOffConfirmation.bind(this);
        this.notifyDropOffCancellation = this.notifyDropOffCancellation.bind(this);
        this.notifyExpiredRent = this.notifyExpiredRent.bind(this);
        this.notifyStuckRent = this.notifyStuckRent.bind(this);
        this.notifyDirtyDoor = this.notifyDirtyDoor.bind(this);
        this.notifyViolence = this.notifyViolence.bind(this);
        this.notifyDirtyDoor = this.notifyDirtyDoor.bind(this);
        this.notifyPing = this.notifyPing.bind(this);
        this.associateLabel = this.associateLabel.bind(this);
        this.handleCourierLogout = this.handleCourierLogout.bind(this);
        this.handleRentFinishedEvent = this.handleRentFinishedEvent.bind(this);
        this.handleRentFinishedByCourier = this.handleRentFinishedByCourier.bind(this);
        this.handleRentFinishedEmergencyTakeout =
            this.handleRentFinishedEmergencyTakeout.bind(this);
        this.handleRentFinishedMissingParcel = this.handleRentFinishedMissingParcel.bind(this);
        this.changeParcelStatus = this.changeParcelStatus.bind(this);
        this.notifyDoorProblem = this.notifyDoorProblem.bind(this);
        this.handleRentStartingEvent = this.handleRentStartingEvent.bind(this);
        this.handleRentFlowInterruptedEvent = this.handleRentFlowInterruptedEvent.bind(this);
        this.getCouriersAccessCodes = this.getCouriersAccessCodes.bind(this);
        this.maintenanceStatusUpdate = this.maintenanceStatusUpdate.bind(this);
        this.handleBloqCreatedEvent = this.handleBloqCreatedEvent.bind(this);
        this.getBoxNameFromLockerTitle = this.getBoxNameFromLockerTitle.bind(this);
    }

    private async getAccessToken(): Promise<AccessToken> {
        if (this.accessToken?.token === undefined || this.accessToken.validity < new Date()) {
            const oauthToken = await this.inpostClient.getAccessToken();
            this.accessToken = {
                token: oauthToken.access_token,
                validity: new Date(new Date().getTime() + oauthToken.expires_in * 1000),
            };
        }
        return this.accessToken;
    }

    override async notifyExpiredRents(_args: { batchSize: number }): Promise<void> {}

    override async notifyCollectItem(_event: RentEvent): Promise<void> {}

    override async getRentById(_props: {
        partnerRentId: string;
        bloqId: string;
        courier?: string;
        locale: string;
        nodeId: string;
        country?: string;
    }): Promise<Rent[]> {
        const { partnerRentId, country = 'UK', nodeId, courier } = _props;

        const accessToken = await this.getAccessToken();
        const boxMachineName = nodeId;

        const isNumericPartnerRentId = !Number.isNaN(Number(partnerRentId));
        const size = partnerRentId.trim().length;
        const numericPartnerRentId: number = isNumericPartnerRentId ? Number(partnerRentId) : -1;
        const prelabeledParcelOperationType =
            courier != null ? ParcelOperationType.COURIER : ParcelOperationType.PICKUP;
        const courierLogin = courier ?? '';

        const bloqitRents: Rent[] = [];

        const FRANCE = CountryCode.FR;
        const QRCodeRegex = /^H4s[A-Za-z0-9\+\/=]+$/;
        const checkRentByQuickSendCode = 9;
        const checkCreateReverseReturnParcelRent = 10;

        const rules = [
            {
                canHandle: isNumericPartnerRentId && size === checkRentByQuickSendCode,
                getRent: async () => {
                    const rent = await this.getRentByQuickSendCode(
                        accessToken,
                        boxMachineName,
                        country,
                        numericPartnerRentId,
                    );
                    bloqitRents.push(rent);

                    return bloqitRents;
                },
            },
            {
                canHandle: isNumericPartnerRentId && size === checkCreateReverseReturnParcelRent,
                getRent: async () => {
                    const rent = await this.createReverseReturnParcelRent(
                        accessToken,
                        boxMachineName,
                        country,
                        numericPartnerRentId,
                    );

                    bloqitRents.push(rent);

                    return bloqitRents;
                },
            },
            {
                canHandle:
                    country.toLowerCase() === FRANCE.toLowerCase() &&
                    QRCodeRegex.test(partnerRentId),
                getRent: async () => {
                    const rent = await this.getParcelByQRCode(
                        accessToken,
                        boxMachineName,
                        country,
                        partnerRentId,
                    );
                    bloqitRents.push(rent);

                    return bloqitRents;
                },
            },
            {
                canHandle: true,
                getRent: async () => {
                    const rent = await this.getPrelabeledParcel(
                        accessToken,
                        boxMachineName,
                        country,
                        partnerRentId,
                        prelabeledParcelOperationType,
                        courierLogin,
                    );

                    bloqitRents.push(rent);
                },
            },
        ];

        try {
            for (const rule of rules) {
                if (rule.canHandle) {
                    const result = await rule.getRent();
                    if (result) {
                        return result; // Return early if a rent is found
                    }
                }
            }
        } catch (ex) {
            const error = ex as Error;
            this.logger.error({
                message: `Parcel with ${partnerRentId} not found`,
                data: { accessToken, boxMachineName, country, partnerRentId },
                errMessage: error.message,
                errStack: error.stack,
            });
        }

        if (bloqitRents.length === 0) {
            const functionalityError: FunctionalityErrorRequest = {
                timestamp: new Date().toISOString(),
                parcelCode: partnerRentId,
                errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
                errorDescription: `Parcel with packCode ${partnerRentId} not found`,
                sessionType: courier != null ? SessionType.Courier : SessionType.Customer,
                functionalityContext: FunctionalityContext.Unknown,
                courierDocumentNr: courier,
            };

            await this.inpostClient.reportBoxMachineFunctionalityError(
                accessToken,
                boxMachineName,
                country,
                functionalityError,
            );
        }

        return bloqitRents;
    }

    private async getPrelabeledParcel(
        accessToken: AccessToken,
        boxMachineName: string,
        country: string,
        partnerRentId: string,
        prelabeledParcelOperationType: ParcelOperationType,
        courierLogin: string | undefined,
    ): Promise<Rent> {
        const checkPrelabeledParcelResponse = await this.inpostClient.checkPrelabeledParcel(
            accessToken,
            boxMachineName,
            country,
            {
                packCode: partnerRentId,
                operationType: prelabeledParcelOperationType,
                courierLogin,
            },
        );

        const rent = InPostModelsMapper.mapParcelToBloqitRent(
            checkPrelabeledParcelResponse?.payload,
        );
        return rent;
    }

    private async createReverseReturnParcelRent(
        accessToken: AccessToken,
        boxMachineName: string,
        country: string,
        onlyNumbersPartnerRentId: number,
    ): Promise<Rent> {
        const createReverseReturnParcelResponse =
            await this.inpostClient.createReverseReturnParcelFromBoxMachine(
                accessToken,
                boxMachineName,
                country,
                { packCode: onlyNumbersPartnerRentId, directParcel: true },
            );

        const rent = InPostModelsMapper.mapReverseReturnParcelToBloqitRent(
            createReverseReturnParcelResponse,
        );

        return rent;
    }

    private async getRentByQuickSendCode(
        accessToken: AccessToken,
        boxMachineName: string,
        country: string,
        onlyNumbersPartnerRentId: number,
    ): Promise<Rent> {
        const quickSendCodeParcelResponse = await this.inpostClient.getParcelByQuickSendCode(
            accessToken,
            boxMachineName,
            country,
            {
                quickSendCode: onlyNumbersPartnerRentId,
            },
        );

        const rent = InPostModelsMapper.mapParcelToBloqitRent(quickSendCodeParcelResponse?.payload);
        return rent;
    }

    private async getParcelByQRCode(
        accessToken: AccessToken,
        boxMachineName: string,
        country: string,
        partnerRentId: string,
    ): Promise<Rent> {
        const qrCodeParcelResponse = await this.inpostClient.getParcelByQRCode(
            accessToken,
            country,
            {
                qrCode: partnerRentId,
                boxMachineName: boxMachineName,
            },
        );

        const rent = InPostModelsMapper.mapQRCodeParcelToBloqitRent(qrCodeParcelResponse);
        return rent;
    }

    override async canBePickedUp(_props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
    }): Promise<boolean> {
        throw new Error('Method not implemented.');
    }

    override async setConfig(
        _changeState: BloqChangeState | LockerChangeState,
    ): Promise<BloqChangeStateResponse | LockerChangeStateResponse> {
        throw new Error('Method not implemented.');
    }

    override async authenticateCourier(_args: {
        bloqExternalId?: string;
        password?: string;
        courierId?: string;
        country?: string;
    }): Promise<AuthenticationResult> {
        const accessToken = await this.getAccessToken();
        const { bloqExternalId, password, courierId: ID, country } = _args;

        if (!bloqExternalId || !password || !ID || !country) {
            throw new Error('Missing required parameters: bloqExternalId, password, ID, country');
        }

        const courierAuthData = await this.inpostClient.courierUpdateAdHoc(
            accessToken,
            ID,
            bloqExternalId,
            country,
        );

        if (courierAuthData.payload === undefined) {
            throw new Error('Courier data is undefined');
        }

        const encryptedPassword = crypto.createHash('md5').update(password).digest('hex');

        const success = courierAuthData.payload?.password === encryptedPassword;

        return { success };
    }

    override async notifyDropOffConfirmation(_event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const role: string = _event.actionInitiatedBy?.role ?? Roles.CUSTOMER;
        const countryCode: string = _event.metadata.country;
        const boxMachineName: string = _event.metadata.bloqExternalID ?? '';

        await this.changeParcelStatus(
            _event,
            role === Roles.COURIER
                ? ParcelStatus.PLACED_BY_COURIER
                : ParcelStatus.PLACED_BY_CUSTOMER,
            accessToken,
            boxMachineName,
            countryCode,
        );
    }

    override async notifyDropOffCancellation(_event: RentEvent): Promise<void> {}

    override async notifyExpiredRent(_event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const countryCode: string = _event.metadata.country;
        const boxMachineName: string = _event.metadata.bloqExternalID ?? '';

        await this.changeParcelStatus(
            _event,
            ParcelStatus.EXPIRED,
            accessToken,
            boxMachineName,
            countryCode,
        );
    }

    override async notifyStuckRent(_event: RentEvent): Promise<void> {
        const sessionType = this.getSessionType(_event.actionInitiatedBy);
        await this.notifyDoorProblem({
            _event,
            sessionType,
            compartmentState: CompartmentState.Damaged,
            errorCode: FunctionalityErrorRequestErrorCode.DamagedCompartment,
            errorDescription: _event.description ?? 'Door is stuck',
            functionalityContext:
                sessionType === SessionType.Courier
                    ? FunctionalityContext.CourierMode
                    : this.getFunctionalityContextFromTitle(_event.title ?? ''), // TODO this will no longer be necessary once this info is sent in the event
        });
    }

    override async notifyDirtyDoor(_event: RentEvent): Promise<void> {
        const sessionType = this.getSessionType(_event.actionInitiatedBy);
        await this.notifyDoorProblem({
            _event,
            sessionType,
            compartmentState: CompartmentState.Soiled,
            errorCode: FunctionalityErrorRequestErrorCode.DamagedCompartment,
            errorDescription: _event.description ?? 'Door is dirty',
            functionalityContext:
                sessionType === SessionType.Courier
                    ? FunctionalityContext.CourierMode
                    : this.getFunctionalityContextFromTitle(_event.title ?? ''),
        });
    }

    override async notifyViolence(_event: RentEvent): Promise<void> {}

    override async notifyPing(_event: BloqChangeState): Promise<void> {}

    override async associateLabel(_props: {
        partnerRentId: string;
        bloqId: string;
        operationType: OperationType;
        courier: string;
        locale: string;
        code: string;
        bloqExternalId?: string;
        country?: string;
    }): Promise<boolean> {
        const accessToken = await this.getAccessToken();
        const countryCode: string = _props.country ?? '';
        const boxMachineName: string = _props.bloqExternalId ?? '';

        const packExistsResult = await this.inpostClient.packExist(
            accessToken,
            boxMachineName,
            countryCode,
            {
                packCode: _props.partnerRentId,
            },
        );

        if (packExistsResult.payload) {
            const labelAssociation: LabelAssociation = {
                partnerRentId: _props.partnerRentId,
                label: _props.code,
            };

            await this.labelAssociationRepository.create({ labelAssociation: labelAssociation });

            return true;
        } else {
            const functionalityError: FunctionalityErrorRequest = {
                timestamp: new Date().toISOString(),
                parcelCode: _props.partnerRentId,
                errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
                errorDescription: 'Invalid Label',
                sessionType: SessionType.Courier,
                functionalityContext: FunctionalityContext.PickUpNotlabeledParcels,
                courierDocumentNr: _props.courier ?? '',
            };

            await this.inpostClient.reportBoxMachineFunctionalityError(
                accessToken,
                boxMachineName,
                countryCode,
                functionalityError,
            );

            return false;
        }
    }

    override async handleCourierLogout(_event: BloqEvent): Promise<void> {}

    override async handleRentFinishedEvent(_event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const sessionType = this.getSessionType(_event.actionInitiatedBy);
        const countryCode: string = _event.metadata.country;
        const boxMachineName: string = _event.metadata.bloqExternalID ?? '';
        const { actionInitiatedBy } = _event;
        const { role } = actionInitiatedBy;

        if (this.hasPickUpError(_event, 'missing_parcel')) {
            await this.handleRentFinishedMissingParcel(
                _event,
                accessToken,
                boxMachineName,
                countryCode,
                sessionType,
            );
        } else if (_event.functionalityContext === BloqFunctionalityContext.EmergencyTakeoutFlow) {
            await this.handleRentFinishedEmergencyTakeout(
                _event,
                accessToken,
                boxMachineName,
                countryCode,
            );
        } else if (
            role === Roles.COURIER ||
            _event.uiFlowContext === UIFlowContext.COURIER_PICKUP_PARCELS_FLOW ||
            _event.uiFlowContext === UIFlowContext.COURIER_PICKUP_LABELLESS_PARCELS_FLOW ||
            _event.uiFlowContext === UIFlowContext.COURIER_PICKUP_EXPIRED_PARCELS_FLOW
        ) {
            await this.handleRentFinishedByCourier(
                _event,
                accessToken,
                boxMachineName,
                countryCode,
            );
        } else if (
            role === Roles.CUSTOMER ||
            _event.uiFlowContext === UIFlowContext.CUSTOMER_PICKUP_PARCELS_FLOW
        ) {
            await this.handleRentFinishedByCustomer(
                _event,
                accessToken,
                boxMachineName,
                countryCode,
            );
        }
    }

    override async handleRentStartingEvent(_event: RentEvent): Promise<void> {}

    override async handleRentFlowInterruptedEvent(_event: RentEvent): Promise<void> {
        const accessToken = await this.getAccessToken();
        const countryCode: string = _event.metadata.country;
        const boxMachineName: string = _event.metadata.bloqExternalID ?? '';
        const sessionType = this.getSessionType(_event.actionInitiatedBy);

        const functionalityError: FunctionalityErrorRequest = {
            timestamp:
                _event.timestamp instanceof Date
                    ? _event.timestamp.toISOString()
                    : new Date().toISOString(),
            parcelCode: _event.metadata.externalID,
            errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
            errorDescription: _event.description ?? 'Flow interrupted',
            sessionType,
            functionalityContext: this.transformFunctionalityContext(
                _event.functionalityContext ?? '',
            ),
            courierDocumentNr: _event.courier ?? '',
            compartment: await this.getBoxNameFromLockerTitle(
                _event.metadata.lockerExternalID,
                boxMachineName,
            ),
        };

        await this.inpostClient.reportBoxMachineFunctionalityError(
            accessToken,
            boxMachineName,
            countryCode,
            functionalityError,
        );
    }

    override async getCouriersAccessCodes(_props: {
        bloqId: string;
        carrier?: string;
        bloqExternalId?: string;
        country?: string;
    }): Promise<CourierAccessCodesResponse> {
        if (!_props.country || !_props.bloqExternalId) {
            throw new Error('Missing required parameters: country, bloqExternalId');
        }

        const accessToken = await this.getAccessToken();

        const couriers = await this.inpostClient.getCouriersChangedDeltaPage(
            accessToken,
            _props.bloqExternalId,
            _props.country,
            { paging: { limit: 1000, offset: 0 } },
        );
        const couriersRoles = await this.inpostClient.getUserRolesDelta(
            accessToken,
            _props.bloqExternalId,
            _props.country,
            {},
        );

        const response: CourierAccess[] = (couriers.payload?.result ?? [])
            .filter(courier => !_props.carrier || courier.carrier === _props.carrier)
            .map(courier => {
                const userRole = couriersRoles.payload?.changed.find(
                    userRole => userRole.name === courier.userRole,
                );

                return {
                    carrier: courier.carrier,
                    code: '',
                    username: courier.login,
                    password: courier.password,
                    permissions: convertToBloqPermissions(decodePermissions(userRole?.mask ?? 0)),
                    hashAlgorithm: HashAlgorithm.MD5,
                };
            });

        return {
            couriers: response,
        };
    }

    override async maintenanceStatusUpdate(
        _event: LockerMaintenanceStatusUpdateEvent,
    ): Promise<void> {
        try {
            const accessToken = await this.getAccessToken();
            const countryCode: string = _event.metadata?.country ?? '';
            const boxMachineName: string = _event.metadata.bloqExternalID ?? '';

            const compartmentState = this.getCompartmentStateFromMaintenanceStatus(
                _event.metadata.previousState as LockerMaintenanceStatus,
                _event.metadata.newState as LockerMaintenanceStatus,
                _event.metadata.reason as LockerMaintenanceStatusReason,
            );

            if (compartmentState === '') {
                this.logger.info({
                    message: `Unknown locker state received: ${_event.metadata.previousState} to ${_event.metadata.newState}`,
                    data: {
                        previousState: _event.metadata.previousState,
                        newState: _event.metadata.newState,
                        reason: _event.metadata.reason,
                        boxMachineName: _event.metadata.bloqExternalID,
                        lockerTitle: _event.metadata.lockerExternalID,
                        bloqId: _event.bloq,
                        lockerId: _event.locker,
                    },
                });

                return;
            }

            const compartmentStatePayload: ReportCompartmentStates = {
                timestamp: new Date().toISOString(),
                sessionType: this.getSessionType(_event.actionInitiatedBy),
                state: compartmentState,
                names: [
                    await this.getBoxNameFromLockerTitle(
                        _event.metadata.lockerExternalID,
                        boxMachineName,
                    ),
                ],
            };

            await this.inpostClient.reportCompartmentStates(
                accessToken,
                countryCode,
                boxMachineName,
                compartmentStatePayload,
            );
        } catch (error) {
            this.logger.error({
                message: 'Error during maintenance status update',
                data: {
                    previousState: _event.metadata.previousState,
                    newState: _event.metadata.newState,
                    reason: _event.metadata.reason,
                },
                errMessage: (error as Error).message,
            });
        }
    }

    override async handleBloqCreatedEvent(_event: BloqEvent): Promise<void> {
        try {
            const accessToken = await this.getAccessToken();
            const bloq = await this.bloqitClient.retrieveBloqById({
                bloqId: _event.bloq,
            });

            if (!bloq) {
                this.logger.error({
                    message: `Bloq with ID ${_event.bloq} not found`,
                    data: { bloqId: _event.bloq },
                });
                return;
            }

            const boxMachineName = bloq.externalId ?? _event.metadata.bloqExternalID;

            if (!boxMachineName) {
                this.logger.error({
                    message: `Bloq with ID ${_event.bloq} has no externalId`,
                    data: { bloqId: _event.bloq },
                });
                return;
            }

            const countryCode = bloq.details?.country;

            if (!countryCode) {
                this.logger.error({
                    message: `Bloq with ID ${_event.bloq} has no country code`,
                    data: { bloqId: _event.bloq },
                });
                return;
            }

            const transformedLockers = InPostModelsMapper.transformLockers(
                bloq.lockers,
                bloq._id,
                boxMachineName,
            );

            this.mappedLockerRepository.create({ mappedLocker: transformedLockers });

            const machineLayoutRequest =
                InPostModelsMapper.mapToMachineLayoutRequest(transformedLockers);

            await this.inpostClient.setBoxMachineLayout(
                accessToken,
                boxMachineName,
                countryCode,
                machineLayoutRequest,
            );
        } catch (error) {
            this.logger.error({
                message: 'Error during Bloq creation event handling',
                data: { bloqId: _event.bloq, error: (error as Error).message },
            });
        }
    }

    override async handleBloqUpdatedEvent(_event: BloqEvent): Promise<void> {
        try {
            const accessToken = await this.getAccessToken();
            const bloq = await this.bloqitClient.retrieveBloqById({
                bloqId: _event.bloq,
            });

            if (!bloq) {
                this.logger.error({
                    message: `Bloq with ID ${_event.bloq} not found`,
                    data: { bloqId: _event.bloq },
                });
                return;
            }

            const boxMachineName = bloq.externalId ?? _event.metadata.bloqExternalID;

            if (!boxMachineName) {
                this.logger.error({
                    message: `Bloq with ID ${_event.bloq} has no externalId`,
                    data: { bloqId: _event.bloq },
                });
                return;
            }

            const countryCode = bloq.details?.country;

            if (!countryCode) {
                this.logger.error({
                    message: `Bloq with ID ${_event.bloq} has no country code`,
                    data: { bloqId: _event.bloq },
                });
                return;
            }

            const transformedLockers = InPostModelsMapper.transformLockers(
                bloq.lockers,
                bloq._id,
                boxMachineName,
            );

            // inpost always overwrites the layout, so we can remove the old one and create a new one
            await this.mappedLockerRepository.remove({ bloqId: bloq._id });
            await this.mappedLockerRepository.create({ mappedLocker: transformedLockers });

            const machineLayoutRequest =
                InPostModelsMapper.mapToMachineLayoutRequest(transformedLockers);

            await this.inpostClient.setBoxMachineLayout(
                accessToken,
                boxMachineName,
                countryCode,
                machineLayoutRequest,
            );
        } catch (error) {
            this.logger.error({
                message: 'Error during Bloq creation event handling',
                data: { bloqId: _event.bloq, error: (error as Error).message },
            });
        }
    }

    private readonly stateTransitionMap: TransitionMap = {
        [LockerMaintenanceStatusReason.DIRTY]: [
            {
                from: LockerMaintenanceStatus.NONE,
                to: LockerMaintenanceStatus.TRIAGE,
                result: CompartmentState.Soiled,
            },
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotSoiled,
            },
        ],
        [LockerMaintenanceStatusReason.DOOR_NOT_OPEN]: [
            {
                from: LockerMaintenanceStatus.NONE,
                to: LockerMaintenanceStatus.TRIAGE,
                result: CompartmentState.Damaged,
            },
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotInspection,
            },
        ],
        [LockerMaintenanceStatusReason.DOOR_NOT_CLOSE]: [
            {
                from: LockerMaintenanceStatus.NONE,
                to: LockerMaintenanceStatus.TRIAGE,
                result: CompartmentState.Damaged,
            },
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotInspection,
            },
        ],
        [LockerMaintenanceStatusReason.DAMAGED]: [
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotInspection,
            },
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.VERIFICATION,
                result: CompartmentState.Inspection,
            },
            {
                from: LockerMaintenanceStatus.VERIFICATION,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotDamaged,
            },
            {
                from: LockerMaintenanceStatus.VERIFICATION,
                to: LockerMaintenanceStatus.MAINTENANCE,
                result: CompartmentState.Verified,
            },
        ],
        [LockerMaintenanceStatusReason.COMPARTMENT_DAMAGED]: [
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotInspection,
            },
            {
                from: LockerMaintenanceStatus.TRIAGE,
                to: LockerMaintenanceStatus.VERIFICATION,
                result: CompartmentState.Inspection,
            },
            {
                from: LockerMaintenanceStatus.VERIFICATION,
                to: LockerMaintenanceStatus.NONE,
                result: CompartmentState.NotDamaged,
            },
            {
                from: LockerMaintenanceStatus.VERIFICATION,
                to: LockerMaintenanceStatus.MAINTENANCE,
                result: CompartmentState.Verified,
            },
        ],
        [LockerMaintenanceStatusReason.OTHER]: [],
    };

    private getCompartmentStateFromMaintenanceStatus(
        previousState: LockerMaintenanceStatus,
        newState: LockerMaintenanceStatus,
        reason: LockerMaintenanceStatusReason,
    ): string {
        const transitions = this.stateTransitionMap[reason];

        if (!transitions) {
            return '';
        }

        const matchingTransition = transitions.find(
            transition =>
                transition.from.toLowerCase() === previousState.toLowerCase() &&
                transition.to.toLowerCase() === newState.toLowerCase(),
        );

        return matchingTransition?.result || '';
    }

    private transformFunctionalityContext(functionalityContext: string): FunctionalityContext {
        if (
            !Object.values(BloqFunctionalityContext).includes(
                functionalityContext as BloqFunctionalityContext,
            )
        ) {
            throw new Error(`Invalid Functionality Context value: ${functionalityContext}`);
        }

        const result = {
            [BloqFunctionalityContext.EmergencyTakeoutFlow]: FunctionalityContext.EmergencyTakeout,
            [BloqFunctionalityContext.CleanCompartmentsFlow]: FunctionalityContext.CleanSoiledBoxes,
            [BloqFunctionalityContext.CourierDropOffFlow]: FunctionalityContext.PutInParcels,
            [BloqFunctionalityContext.CourierPickUpExpiredParcelsFlow]:
                FunctionalityContext.PickUpExpiredParcels,
            [BloqFunctionalityContext.CourierPickUpParcelsFlow]:
                FunctionalityContext.PickUpPrelabeledParcels,
            [BloqFunctionalityContext.CourierPickUpLabellessParcelsFlow]:
                FunctionalityContext.PickUpNotlabeledParcels,
            [BloqFunctionalityContext.CourierInspectionFlow]:
                FunctionalityContext.InspectionCompartments,
            [BloqFunctionalityContext.CustomerDropOffLabellessParcelsFlow]:
                FunctionalityContext.CustomerSendNotLabeledParcel,
            [BloqFunctionalityContext.CustomerDropOffParcelsFlow]:
                FunctionalityContext.CustomerSendPrelabeledParcel,
            [BloqFunctionalityContext.CustomerPickUpParcelsFlow]:
                FunctionalityContext.CustomerCollectParcel,
        }[functionalityContext as BloqFunctionalityContext];

        if (!result)
            throw new Error(`Invalid Functionality Context value: ${functionalityContext}`);
        return result;
    }

    private async handleRentFinishedByCourier(
        _event: RentEvent,
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
    ): Promise<void> {
        const labelAssociation = await this.labelAssociationRepository.retrieve({
            parcelCode: _event.metadata.externalID,
        });
        let newParcelCode: string | undefined = undefined;
        if (labelAssociation) {
            newParcelCode = labelAssociation.label;
        }

        if (_event.metadata.pickUpInfo?.success) {
            await this.changeParcelStatus(
                _event,
                ParcelStatus.TAKEN_BY_COURIER,
                accessToken,
                boxMachineName,
                countryCode,
                undefined,
                newParcelCode,
            );

            if (newParcelCode) {
                await this.labelAssociationRepository.remove({
                    parcelCode: _event.metadata.externalID,
                });
            }
        }
    }

    private async handleRentFinishedByCustomer(
        _event: RentEvent,
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
    ): Promise<void> {
        if (_event.metadata.pickUpInfo?.success) {
            await this.changeParcelStatus(
                _event,
                ParcelStatus.DELIVERED,
                accessToken,
                boxMachineName,
                countryCode,
            );
        }
    }

    private async handleRentFinishedEmergencyTakeout(
        _event: RentEvent,
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
    ): Promise<void> {
        await this.changeParcelStatus(
            _event,
            ParcelStatus.EMERGENCY_DELIVERY,
            accessToken,
            boxMachineName,
            countryCode,
        );

        await this.inpostClient.checkPrelabeledParcel(accessToken, boxMachineName, countryCode, {
            packCode: _event.metadata.externalID,
            operationType: ParcelOperationType.EMERGENCY_DELIVERY,
        });
    }

    private async handleRentFinishedMissingParcel(
        _event: RentEvent,
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        sessionType: SessionType,
    ): Promise<void> {
        const functionalityError: FunctionalityErrorRequest = {
            timestamp:
                _event.timestamp instanceof Date
                    ? _event.timestamp.toISOString()
                    : new Date().toISOString(),
            parcelCode: _event.metadata.externalID,
            errorCode: FunctionalityErrorRequestErrorCode.ParcelDoesNotExistInCentralSystem,
            errorDescription: _event.description ?? 'Parcel is missing',
            sessionType,
            functionalityContext:
                sessionType === SessionType.Courier
                    ? FunctionalityContext.CourierMode
                    : this.getFunctionalityContextFromTitle(_event.title ?? ''),
            courierDocumentNr: _event.courier ?? '',
            compartment: await this.getBoxNameFromLockerTitle(
                _event.metadata.lockerExternalID,
                boxMachineName,
            ),
        };

        await this.inpostClient.reportBoxMachineFunctionalityError(
            accessToken,
            boxMachineName,
            countryCode,
            functionalityError,
        );

        await this.changeParcelStatus(
            _event,
            ParcelStatus.MISSING_PARCEL,
            accessToken,
            boxMachineName,
            countryCode,
        );
    }

    private async changeParcelStatus(
        _event: RentEvent,
        status: ParcelStatus,
        accessToken: AccessToken,
        boxMachineName: string,
        countryCode: string,
        courierDocumentNr?: string,
        newParcelCode?: string,
    ): Promise<void> {
        const changeParcelStatusRequestData: ChangeParcelStatusRequest = {
            parcelCode: [_event.metadata.externalID],
            status,
            boxName: await this.getBoxNameFromLockerTitle(
                _event.metadata?.lockerExternalID,
                boxMachineName,
            ),
            boxSize: getBoxSizeFromLockerType(String(_event.metadata?.lockerSize)),
            timestamp: _event.timestamp.toString(),
            actionSource: this.getActionSource(_event.actionInitiatedBy.source),
        };

        if (newParcelCode != null) {
            changeParcelStatusRequestData.newParcelCode = newParcelCode;
        }

        if (courierDocumentNr != null) {
            changeParcelStatusRequestData.courierDocumentNr = courierDocumentNr;
        }

        await this.inpostClient.changeParcelStatus(
            accessToken,
            boxMachineName,
            countryCode,
            changeParcelStatusRequestData,
        );
    }

    private async getBoxNameFromLockerTitle(
        lockerTitle: string,
        bloqExternalId: string,
    ): Promise<string> {
        const boxes = await this.mappedLockerRepository.retrieveBySharedId({
            sharedId: bloqExternalId,
        });

        if (boxes === undefined || boxes.layout.length === 0) {
            this.logger.error({
                message: `No mapped lockers found for bloqExternalID: ${bloqExternalId}`,
                data: { bloqExternalID: bloqExternalId },
            });
            return '';
        }

        const box = boxes.layout.find(box => box.lockerTitle === lockerTitle);

        if (!box) {
            this.logger.error({
                message: `No locker found for lockerTitle: ${lockerTitle}`,
                data: { lockerTitle: lockerTitle },
            });
            return '';
        }

        return box.partnerName;
    }

    private hasPickUpError(_event: RentEvent, error: string): boolean {
        return (
            Array.isArray(_event.metadata.pickUpInfo?.errorReasons) &&
            Boolean(
                _event.metadata.pickUpInfo.errorReasons.some(
                    r => typeof r === 'string' && r.toLowerCase().includes(error),
                ),
            )
        );
    }

    private getSessionType(actionInitiatedBy: ActionInitiatedBy): SessionType {
        return actionInitiatedBy.role === Roles.COURIER
            ? SessionType.Courier
            : SessionType.Customer;
    }

    private getActionSource(source?: ActionInitiatedBySources): ActionSource {
        return source === ActionInitiatedBySources.LOCKER
            ? ActionSource.PANEL
            : ActionSource.WEB_SERVICE;
    }

    private async notifyDoorProblem(_props: {
        _event: RentEvent;
        sessionType: SessionType;
        compartmentState: CompartmentState;
        errorCode: FunctionalityErrorRequestErrorCode;
        errorDescription: string;
        functionalityContext: FunctionalityContext;
    }): Promise<void> {
        const accessToken = await this.getAccessToken();
        const boxMachineName: string = _props._event.metadata.bloqExternalID ?? _props._event.bloq;

        const countryCode: string = _props._event.metadata?.country ?? '';

        const compartmentStates: ReportCompartmentStates = {
            timestamp:
                _props._event.timestamp instanceof Date
                    ? _props._event.timestamp.toISOString()
                    : new Date().toISOString(),
            sessionType: _props.sessionType,
            state: _props.compartmentState,
            names: [
                await this.getBoxNameFromLockerTitle(
                    _props._event.metadata?.lockerExternalID,
                    boxMachineName,
                ),
            ],
        };

        await this.inpostClient.reportCompartmentStates(
            accessToken,
            countryCode,
            _props._event.metadata?.bloqExternalID,
            compartmentStates,
        );

        const functionalityError: FunctionalityErrorRequest = {
            timestamp:
                _props._event.timestamp instanceof Date
                    ? _props._event.timestamp.toISOString()
                    : new Date().toISOString(),
            parcelCode: _props._event.metadata.externalID,
            errorCode: _props.errorCode,
            errorDescription: _props.errorDescription,
            sessionType: _props.sessionType,
            functionalityContext: _props.functionalityContext,
            courierDocumentNr: _props._event.courier ?? '',
            compartment: await this.getBoxNameFromLockerTitle(
                _props._event.metadata?.lockerExternalID,
                boxMachineName,
            ),
        };

        await this.inpostClient.reportBoxMachineFunctionalityError(
            accessToken,
            boxMachineName,
            countryCode,
            functionalityError,
        );
    }

    private getFunctionalityContextFromTitle(title: string): FunctionalityContext {
        if (title?.toLowerCase().includes('pickup')) {
            return FunctionalityContext.CustomerCollectParcel;
        } else if (title?.toLowerCase().includes('return')) {
            return FunctionalityContext.CustomerReturnParcel;
        } else {
            return FunctionalityContext.Unknown;
        }
    }
}
