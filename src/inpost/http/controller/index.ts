import { type BloqitClient } from '../../../bloqit/sdk';
import { type Logger } from '../../../core/logger';
import { type HTTPRequest, type HTTPResponse } from '../../../http/models/extensions/express';
import * as httpCodes from '../../../http/models/codes';
import {
    BoxMachineParcelStatus,
    BoxSize,
    InboundCompartmentState,
    type CompartmentInfo,
    type ParcelInfo,
} from '../../models';
import {
    LockerMaintenanceStatus,
    LockerMaintenanceStatusReason,
    LockerType,
} from '../../../bloqit/lockers/models';
import { type MachineBlockRequest } from '../dto/requests/machine-block-request-body';
import { InPostBaseResponseFactory, Status } from '../dto/responses/inpost-base-response';
import { type BloqObjectResponse } from '../../../bloqit/bloqs/models';
import {
    type OpenCompartmentDto,
    OpenDoorStatus,
} from '../dto/responses/open-compartments-response';

import { type OpenCompartmentsRequest } from '../dto/requests/open-compartments-request';
import { type CreateParcelRequest } from '../dto/requests/create-parcel-request';
import {
    CreateRentResponse,
    PrePickupAction,
    PrePickupActionType,
    RentObjectResponse,
    RentState,
    UpdateRentResponse,
} from '../../../bloqit/rents/models/rent';
import { getLockerTypeFromBoxSize, getParcelStatusFromRentState } from '../../common';
import { UpdateCompartmentRequest } from '../dto/requests/update-compartment-request';
import crypto from 'crypto';
import { InPostModelsMapper } from '../../models/models-mapper';
import { CompartmentInfoState } from '../../models/enums';
import { MappedLockerRepository } from '../../data-access/repositories/mapped-locker/mapped-locker-repository';

export class InPostInboundController {
    private readonly bloqitClient: BloqitClient;
    private readonly logger: Logger;
    private mappedLockerRepository: MappedLockerRepository;

    constructor(deps: {
        bloqitClient: BloqitClient;
        logger: Logger;
        mappedLockerRepository: MappedLockerRepository;
    }) {
        this.bloqitClient = deps.bloqitClient;
        this.logger = deps.logger;
        this.mappedLockerRepository = deps.mappedLockerRepository;

        this.healthcheck = this.healthcheck.bind(this);
        this.getCompartmentInfo = this.getCompartmentInfo.bind(this);
        this.getParcelInfo = this.getParcelInfo.bind(this);
        this.getBoxSizeFromLockerType = this.getBoxSizeFromLockerType.bind(this);
        this.toggleMachineBlock = this.toggleMachineBlock.bind(this);
        this.openCompartments = this.openCompartments.bind(this);
        this.createOrUpdateParcel = this.createOrUpdateParcel.bind(this);
        this.expireRent = this.expireRent.bind(this);
        this.deliverParcel = this.deliverParcel.bind(this);
        this.deleteParcel = this.deleteParcel.bind(this);
        this.updateCompartment = this.updateCompartment.bind(this);
        this.getNewLockerState = this.getNewLockerState.bind(this);
        this.getProximityCode = this.getProximityCode.bind(this);
        this.createRent = this.createRent.bind(this);
        this.getBoxNameFromLockerTitle = this.getBoxNameFromLockerTitle.bind(this);
        this.getLockerTitleFromBoxName = this.getLockerTitleFromBoxName.bind(this);
    }

    async healthcheck(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API healthcheck',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);

        if (boxMachineName.length === 0) {
            return res.status(httpCodes.BAD_REQUEST).json({
                status: 'error',
                message: 'Box machine name is required',
            });
        }

        const bloqData = await this.getBloqByExternalId(boxMachineName);

        if (bloqData === undefined) {
            this.logger.error({
                message: 'Failed to process healthcheck: No matching box machine found',
            });

            const errorMessage = 'No matching box machine was found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        if (!bloqData.active) {
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: 'Box machine is inactive' }));
        }

        return res.status(httpCodes.OK).json();
    }

    /**
     * Returns info for all compartments of a given box machine
     * @param req
     * @param res
     * @returns
     */
    async getCompartmentInfo(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API getCompartmentInfo',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);

        if (boxMachineName.length === 0) {
            return res.status(httpCodes.BAD_REQUEST).json({
                status: 'error',
                message: 'Box machine name is required',
            });
        }

        try {
            const bloqData = await this.bloqitClient.retrieveBloqByExternalId({
                externalId: boxMachineName,
            });

            if (!bloqData) {
                return res.status(httpCodes.NOT_FOUND).json({
                    status: 'error',
                    message: 'Box machine not found',
                });
            }

            const compartmentInfo: CompartmentInfo[] = await Promise.all(
                bloqData.lockers.map(async locker => {
                    const parcelInfo = await this.getParcelInfo(locker.rent ?? '');

                    if (parcelInfo.length > 0) {
                        parcelInfo[0].size = this.getBoxSizeFromLockerType(locker.type);
                    }

                    return {
                        name: await this.getBoxNameFromLockerTitle(
                            locker.lockerTitle,
                            boxMachineName,
                        ),
                        isBusy: locker.rent !== null,
                        isOpened: locker.isOpen,
                        parcelInfo,
                        size: this.getBoxSizeFromLockerType(locker.type),
                        state: locker.active
                            ? CompartmentInfoState.EFFICIENT
                            : CompartmentInfoState.BROKEN,
                    };
                }),
            );

            return res.json({ compartmentInfo });
        } catch (error) {
            this.logger.error({
                message: 'Error getting compartment info',
                error,
            });

            return res.status(httpCodes.INTERNAL_SERVER_ERROR).json({
                status: 'error',
                message: 'Error getting compartment info',
            });
        }
    }

    async openCompartments(req: OpenCompartmentsRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API openDoors',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);
        const boxNames = req.body.boxList;
        const ALL_LOCKERS = 'ALL';

        const allLockers = boxNames.length === 1 && boxNames[0].toUpperCase() === ALL_LOCKERS;

        const mappedLockerNames = allLockers
            ? await this.getAllLockersFromBloq(boxMachineName)
            : await Promise.all(
                  boxNames.map(box => this.getLockerTitleFromBoxName(box, boxMachineName)),
              );

        if (!boxMachineName || !mappedLockerNames) {
            return res.status(httpCodes.BAD_REQUEST).json({
                status: 'error',
                message: 'boxMachineName and boxList are required',
            });
        }

        let bloqData: BloqObjectResponse | undefined;
        try {
            bloqData = await this.bloqitClient.retrieveBloqByExternalId({
                externalId: boxMachineName,
            });
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error retrieving bloq data',
                errorMessage: err.message,
                errorStack: err.stack,
            });
        }

        try {
            if (bloqData === undefined) {
                this.logger.error({
                    message:
                        'Failed to process openCompartments request. No matching box machine was found on bloqit side',
                });

                const errorMessage = 'No matching box machine was found';
                return res
                    .status(httpCodes.BAD_REQUEST)
                    .json(InPostBaseResponseFactory.error({ message: errorMessage }));
            }

            // filter sent doors to bloqit lockers to get the correct ids
            const lockersToOpen = bloqData.lockers.flatMap(locker => {
                if (mappedLockerNames.includes(locker.lockerTitle)) {
                    return {
                        id: locker._id,
                        lockerTitle: locker.lockerTitle,
                        openStatus: OpenDoorStatus.FAILED,
                        rent: locker.rent,
                    };
                }
                return [];
            });

            const lockersToOpenWithRent = lockersToOpen.filter(
                locker => locker.rent !== undefined && locker.rent !== null,
            );
            const lockersToOpenWithoutRent = lockersToOpen.filter(
                locker => locker.rent === undefined || locker.rent === null,
            );

            if (lockersToOpenWithoutRent.length > 0) {
                const openDoorsResponse = await this.bloqitClient.openDoors({
                    bloqId: bloqData._id,
                    lockers: lockersToOpenWithoutRent.map(locker => locker.id),
                });

                if (
                    openDoorsResponse.status !== httpCodes.OK &&
                    openDoorsResponse.status !== httpCodes.CONFLICT
                ) {
                    return res.status(httpCodes.INTERNAL_SERVER_ERROR).json({
                        status: 'error',
                        message: 'Error opening compartments',
                    });
                }

                // update the status of the lockers
                lockersToOpen.forEach(locker => {
                    locker.openStatus = openDoorsResponse.lockers.includes(locker.id)
                        ? OpenDoorStatus.OPENED
                        : OpenDoorStatus.FAILED;
                });
            }

            // if there are lockers to open with rent, we need to open them individually
            if (lockersToOpenWithRent.length > 0) {
                for (const locker of lockersToOpenWithRent) {
                    if (locker.rent !== null && locker.rent !== undefined) {
                        const response = await this.bloqitClient.openDoorWithRent({
                            rentId: locker.rent,
                        });

                        // if response was successful, update the locker status
                        const targetLocker = lockersToOpen.find(l => l.id === locker.id);
                        if (targetLocker) {
                            targetLocker.openStatus =
                                response.locker === locker.id
                                    ? OpenDoorStatus.OPENED
                                    : OpenDoorStatus.FAILED;
                        }
                    }
                }
            }

            const boxListResponse: OpenCompartmentDto[] = lockersToOpen.map(locker => ({
                boxName: locker.lockerTitle,
                status: locker.openStatus,
            }));

            const openCompartmentsResponse = {
                boxlist: boxListResponse,
            };

            return res.status(httpCodes.OK).json(
                InPostBaseResponseFactory.success({
                    message: 'Compartments opened successfully',
                    payload: openCompartmentsResponse,
                }),
            );
        } catch (error) {
            const err = error as Error;

            // core returns 409 when trying to open compartments that are already open
            if (err.message?.toLowerCase().includes('409')) {
                return res.status(httpCodes.OK).json(
                    InPostBaseResponseFactory.success({
                        message: 'Compartments opened successfully',
                    }),
                );
            }

            this.logger.error({
                message: 'Error opening compartments',
                errMessage: err.message,
                errStack: err.stack,
            });

            return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                InPostBaseResponseFactory.error({
                    message: 'Error opening compartments',
                }),
            );
        }
    }

    /**
     * Toggle the machine block status
     * @param req - The request object containing the machineId and block status
     * @param res - The response object
     * @returns - The response object with the result of the operation
     */
    async toggleMachineBlock(req: MachineBlockRequest, res: HTTPResponse): Promise<HTTPResponse> {
        const { block } = req.body;
        const boxMachineName = this.getBoxMachineNameFromHeaders(req);

        this.logger.info({
            message: 'InPost API toggleMachineBlock',
            method: req.method,
            url: req.url,
            boxMachineName,
            block,
        });

        let bloqData: BloqObjectResponse | undefined;
        try {
            bloqData = await this.bloqitClient.retrieveBloqByExternalId({
                externalId: boxMachineName,
            });
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error retrieving bloq data',
                errorMessage: err.message,
                errorStack: err.stack,
            });
        }

        try {
            if (bloqData === undefined) {
                this.logger.error({
                    message:
                        'Failed to process toggleMachineBlock request. No matching box machine was found on bloqit side',
                });

                const errorMessage = 'No matching box machine was found';
                return res
                    .status(httpCodes.BAD_REQUEST)
                    .json(InPostBaseResponseFactory.error({ message: errorMessage }));
            }

            const bloqitBloqId = bloqData._id;

            if (block) {
                await this.bloqitClient.deactivateBloq({ bloqId: bloqitBloqId });
            } else {
                await this.bloqitClient.activateBloq({ bloqId: bloqitBloqId });
            }

            return res.status(httpCodes.OK).json(
                InPostBaseResponseFactory.success({
                    message: `Machine ${boxMachineName} has been ${block ? 'blocked' : 'unblocked'}`,
                }),
            );
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error toggling machine block',
                errorMessage: err.message,
                errorStack: err.stack,
            });
            return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                InPostBaseResponseFactory.error({
                    message: 'Failed to toggle machine block',
                }),
            );
        }
    }

    /**
     * Create a parcel
     * @param req
     * @param res
     * @returns
     */
    async createOrUpdateParcel(req: CreateParcelRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API createParcel',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);

        if (!boxMachineName) {
            return res.status(httpCodes.BAD_REQUEST).json(
                InPostBaseResponseFactory.error({
                    message: 'boxMachineName is required',
                }),
            );
        }

        let bloqData: BloqObjectResponse | undefined;

        try {
            bloqData = await this.bloqitClient.retrieveBloqByExternalId({
                externalId: boxMachineName,
            });
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error retrieving bloq data',
                errorMessage: err.message,
                errorStack: err.stack,
            });
        }

        if (bloqData === undefined) {
            this.logger.error({
                message:
                    'Failed to process createOrUpdateParcel request. No matching box machine was found on bloqit side',
            });

            const errorMessage = 'No matching box machine was found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        let existingRent: RentObjectResponse | undefined;

        try {
            existingRent = await this.bloqitClient.retrieveRentByExternalId({
                externalId: req.body.parcelCode,
            });
            this.logger.info({
                message: 'InPost API create/update Parcel - Existing Rent',
                method: req.method,
                url: req.url,
                data: { existingRent },
            });
        } catch (error) {
            this.logger.info({
                message: 'Existing rent not found. Creating a new rent',
            });

            try {
                const createdRent = await this.createRent(req, bloqData, res);

                return res.status(httpCodes.OK).json(
                    InPostBaseResponseFactory.success({
                        message: 'Parcel created successfully',
                        payload: { createdRent },
                    }),
                );
            } catch (error) {
                const err = error as Error;
                this.logger.error({
                    message: 'Error creating rent',
                    errorMessage: err.message,
                    errorStack: err.stack,
                });
                return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                    InPostBaseResponseFactory.error({
                        message: 'Error creating parcel',
                    }),
                );
            }
        }

        let updatedRent;

        try {
            existingRent.externalID = req.body.parcelCode;
            existingRent.details.pickUpCodes.pin = req.body.openCode;

            updatedRent = await this.bloqitClient.updateRent({
                rentId: existingRent._id,
                rent: existingRent,
            });
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error updating rent',
                errorMessage: err.message,
                errorStack: err.stack,
            });
            return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                InPostBaseResponseFactory.error({
                    message: 'Error updating parcel',
                }),
            );
        }

        let parcel;

        try {
            parcel = InPostModelsMapper.mapBloqitRentToParcelTempFix(updatedRent);
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error mapping rent to parcel',
                data: { updatedRent },
                errorMessage: err.message,
                errorStack: err.stack,
            });
            return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                InPostBaseResponseFactory.error({
                    message: 'Error updating parcel',
                }),
            );
        }

        const currentParcelStatus = getParcelStatusFromRentState(existingRent.state);
        const newParcelStatus = req.body.status;

        this.logger.info({
            message: 'InPost API create/update Parcel - After Update Rent',
            method: req.method,
            url: req.url,
            data: { existingRent, currentParcelStatus, newParcelStatus, parcel },
        });

        if (currentParcelStatus !== newParcelStatus) {
            const handlers = {
                [BoxMachineParcelStatus.EXPIRED]: async () => {
                    try {
                        const expiredRent = await this.expireRent(existingRent._id);
                        return {
                            message: 'Parcel expired successfully',
                            payload: { parcel: expiredRent },
                        };
                    } catch (error) {
                        const err = error as Error;
                        this.logger.error({
                            message: 'Error expiring parcel',
                            errorMessage: err.message,
                            errorStack: err.stack,
                        });
                        return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                            InPostBaseResponseFactory.error({
                                message: 'Error expiring parcel',
                            }),
                        );
                    }
                },
                [BoxMachineParcelStatus.CLAIMED]: async () => {
                    try {
                        const claimedRent = await this.bloqitClient.cancelRent({
                            rentId: existingRent._id,
                        });
                        return {
                            message: 'Parcel claimed successfully',
                            payload: { parcel: claimedRent },
                        };
                    } catch (error) {
                        const err = error as Error;
                        this.logger.error({
                            message: 'Error claiming parcel',
                            errorMessage: err.message,
                            errorStack: err.stack,
                        });
                        return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                            InPostBaseResponseFactory.error({
                                message: 'Error claiming parcel',
                            }),
                        );
                    }
                },
                [BoxMachineParcelStatus.NOT_AVAILABLE]: async () => {
                    try {
                        if (existingRent.state !== RentState.FINISHED) {
                            await this.bloqitClient.cancelRent({
                                rentId: existingRent._id,
                            });

                            const notAvailableRent = await this.bloqitClient.finishRent({
                                rentId: existingRent._id,
                            });

                            return {
                                message: 'Parcel marked as not available successfully',
                                payload: { parcel: notAvailableRent },
                            };
                        } else {
                            return {
                                message: 'Parcel is already marked as not available',
                                payload: { parcel },
                            };
                        }
                    } catch (error) {
                        const err = error as Error;
                        this.logger.error({
                            message: 'Error marking parcel as not available',
                            errorMessage: err.message,
                            errorStack: err.stack,
                        });
                        return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                            InPostBaseResponseFactory.error({
                                message: 'Error marking parcel as not available',
                            }),
                        );
                    }
                },
                [BoxMachineParcelStatus.STORED]: async () => {
                    return {
                        message: 'Parcel can be picked up by customer',
                        payload: { parcel },
                    };
                },
            };

            const handler = handlers[newParcelStatus];

            if (handler) {
                const result = await handler();

                if (result) {
                    return res.status(httpCodes.OK).json(InPostBaseResponseFactory.success(result));
                }
            }
        }

        return res.status(httpCodes.OK).json(
            InPostBaseResponseFactory.success({
                message: 'Parcel updated successfully',
                payload: { parcel },
            }),
        );
    }

    /**
     * Sets the parcel status to delivered
     * @param req - The request object containing the parcel information
     * @param res - The response object
     * @returns - The response object with the result of the operation
     */
    async deliverParcel(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API deliverParcel',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);
        const { parcelCode } = req.params;

        if (!boxMachineName || !parcelCode) {
            return res.status(httpCodes.BAD_REQUEST).json(
                InPostBaseResponseFactory.error({
                    message: 'boxMachineName and parcelCode are required',
                }),
            );
        }

        const bloqData = await this.getBloqByExternalId(boxMachineName);

        if (bloqData === undefined) {
            this.logger.error({
                message:
                    'Failed to process deliverParcel request. No matching box machine was found on bloqit side',
            });

            const errorMessage = 'No matching box machine was found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        const rentData = await this.getRentByExternalId(parcelCode);

        if (rentData === undefined) {
            this.logger.error({
                message:
                    'Failed to process deliverParcel request. No matching rent was found on bloqit side',
            });

            const errorMessage = 'Parcel not found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        try {
            if (rentData.bloq !== bloqData._id) {
                return res.status(httpCodes.BAD_REQUEST).json(
                    InPostBaseResponseFactory.error({
                        message: 'Parcel does not belong to this box machine',
                    }),
                );
            }

            if (!['in_progress', 'expired', 'cancelled'].includes(rentData.state)) {
                this.logger.error({
                    message: 'Invalid status transition',
                    rentState: rentData.state,
                });
                return res
                    .status(httpCodes.BAD_REQUEST)
                    .json(
                        InPostBaseResponseFactory.error({ message: 'Invalid status transition' }),
                    );
            }

            await this.bloqitClient.collectItem({
                rentId: rentData._id,
            });

            return res.status(httpCodes.OK).json(
                InPostBaseResponseFactory.success({
                    message: 'Parcel delivered successfully',
                }),
            );
        } catch (error) {
            this.logger.error({
                message: 'Error delivering parcel',
                error,
            });

            return res
                .status(httpCodes.INTERNAL_SERVER_ERROR)
                .json(InPostBaseResponseFactory.error({ message: 'Error delivering parcel' }));
        }
    }

    /**
     * Delete parcel in a given box machine
     * @param req - The request object containing the parcel information
     * @param res - The response object
     * @returns - The response object with the result of the operation
     */
    async deleteParcel(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API deleteParcel',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);
        const { packCode } = req.body;

        const bloqData = await this.getBloqByExternalId(boxMachineName);

        if (bloqData === undefined) {
            this.logger.error({
                message:
                    'Failed to process deleteParcel request. No matching box machine was found on bloqit side',
            });

            const errorMessage = 'No matching box machine was found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        const rentData = await this.getRentByExternalId(packCode);

        if (rentData === undefined) {
            this.logger.error({
                message:
                    'Failed to process deleteParcel request. No matching rent was found on bloqit side',
            });

            const errorMessage = 'Parcel not found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        try {
            if (rentData.bloq !== bloqData._id) {
                return res.status(httpCodes.BAD_REQUEST).json(
                    InPostBaseResponseFactory.error({
                        message: 'Parcel does not belong to this box machine',
                    }),
                );
            }

            if (rentData.state !== 'in_progress') {
                return res
                    .status(httpCodes.BAD_REQUEST)
                    .json(
                        InPostBaseResponseFactory.error({ message: 'Parcel is not in progress' }),
                    );
            }

            await this.bloqitClient.cancelRent({
                rentId: rentData._id,
            });
            await this.bloqitClient.finishRent({
                rentId: rentData._id,
            });

            return res.status(httpCodes.OK).json(
                InPostBaseResponseFactory.success({
                    message: 'Parcel deleted successfully',
                }),
            );
        } catch (error) {
            this.logger.error({
                message: 'Error deleting parcel',
                error,
            });

            return res
                .status(httpCodes.INTERNAL_SERVER_ERROR)
                .json(InPostBaseResponseFactory.error({ message: 'Error deleting parcel' }));
        }
    }

    async updateCompartment(
        req: UpdateCompartmentRequest,
        res: HTTPResponse,
    ): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API updateCompartment',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);

        if (!boxMachineName) {
            return res.status(httpCodes.BAD_REQUEST).json(
                InPostBaseResponseFactory.error({
                    message: 'boxMachineName is required',
                }),
            );
        }

        const { boxes, state } = req.body;

        if (!boxes || state == null) {
            return res.status(httpCodes.BAD_REQUEST).json(
                InPostBaseResponseFactory.error({
                    message: 'boxes and state are required',
                }),
            );
        }

        const bloqData = await this.getBloqByExternalId(boxMachineName);

        if (bloqData === undefined) {
            this.logger.error({
                message:
                    'Failed to process updateCompartment request. No matching box machine was found on bloqit side',
            });

            const errorMessage = 'No matching box machine was found';
            return res
                .status(httpCodes.BAD_REQUEST)
                .json(InPostBaseResponseFactory.error({ message: errorMessage }));
        }

        const mappedBoxes = await Promise.all(
            boxes.map(box => this.getLockerTitleFromBoxName(box, boxMachineName)),
        );

        const lockersToUpdate = bloqData.lockers.filter(locker =>
            mappedBoxes.includes(locker.lockerTitle),
        );

        if (lockersToUpdate.length === 0) {
            return res.status(httpCodes.BAD_REQUEST).json(
                InPostBaseResponseFactory.error({
                    message: 'No matching lockers found',
                }),
            );
        }
        const updatedLockers: string[] = [];
        const failedLockers: { compartment: string; failReason: string }[] = [];

        for (const locker of lockersToUpdate) {
            const newState = this.getNewLockerState(state);

            try {
                let updatedLocker = await this.bloqitClient.reportLockerProblem({
                    bloqId: bloqData._id,
                    lockerId: locker._id,
                    state: newState.state,
                    reason: newState.reason,
                });

                const isLockerActive = newState.state === LockerMaintenanceStatus.NONE;

                updatedLocker = await this.bloqitClient.changeLockerAvailability({
                    bloqId: bloqData._id,
                    lockerId: locker._id,
                    active: isLockerActive,
                });

                updatedLockers.push(updatedLocker.lockerTitle);
            } catch (error) {
                this.logger.error({
                    message: `Error updating compartment ${locker._id}`,
                    error,
                });

                failedLockers.push({
                    compartment: locker.lockerTitle,
                    failReason: error as string,
                });
            }
        }

        if (updatedLockers.length > 0) {
            return res.status(httpCodes.OK).json(
                InPostBaseResponseFactory.success({
                    message: 'Compartments updated successfully',
                    payload: {
                        updated: updatedLockers,
                        failed: failedLockers,
                    },
                }),
            );
        } else {
            return res.status(httpCodes.INTERNAL_SERVER_ERROR).json(
                InPostBaseResponseFactory.error({
                    message: 'Error updating compartments',
                }),
            );
        }
    }

    /**
     * Get the current proximity code for a given box machine
     * @param req - The request object containing the box machine name
     * @param res - The response object
     * @returns - The response object with the current proximity code
     */
    async getProximityCode(req: HTTPRequest, res: HTTPResponse): Promise<HTTPResponse> {
        this.logger.info({
            message: 'InPost API getProximityCode',
            method: req.method,
            url: req.url,
        });

        const boxMachineName = this.getBoxMachineNameFromHeaders(req);

        if (!boxMachineName) {
            return res.status(httpCodes.BAD_REQUEST).json({
                status: Status.ERROR,
                message: 'boxMachineName is required',
            });
        }

        return res.status(httpCodes.OK).json({
            proximityCode: this.generateRandomPin(boxMachineName),
        });
    }

    private generateRandomPin(secret: string): string {
        const hash = crypto.createHash('sha256').update(secret, 'utf8').digest('hex');
        const intVal = parseInt(hash.slice(0, 8), 16);
        const digits = intVal % 10000;

        return digits.toString().padStart(4, '0');
    }

    private async getBloqByExternalId(externalId: string): Promise<BloqObjectResponse | undefined> {
        try {
            return await this.bloqitClient.retrieveBloqByExternalId({
                externalId: externalId,
            });
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error retrieving bloq data',
                errorMessage: err.message,
                errorStack: err.stack,
            });
            return undefined;
        }
    }

    private async getRentByExternalId(externalId: string): Promise<RentObjectResponse | undefined> {
        try {
            return await this.bloqitClient.retrieveRentByExternalId({
                externalId,
            });
        } catch (error) {
            const err = error as Error;
            this.logger.error({
                message: 'Error retrieving rent data',
                errorMessage: err.message,
                errorStack: err.stack,
            });
            return undefined;
        }
    }

    private async getParcelInfo(rentId: string): Promise<ParcelInfo[]> {
        try {
            if (!rentId) {
                return [];
            }
            const response = await this.bloqitClient.getRent({ rentId });

            return [
                {
                    code: response.externalID,
                    state: getParcelStatusFromRentState(response.state),
                },
            ];
        } catch (error) {
            this.logger.error({
                message: 'Error getting parcel info',
                error,
            });
            throw error;
        }
    }

    private getBoxSizeFromLockerType(lockerType: string): BoxSize {
        switch (lockerType) {
            case LockerType.S:
                return BoxSize.A;
            case LockerType.M:
                return BoxSize.B;
            case LockerType.L:
                return BoxSize.C;
            case LockerType.XS:
            default:
                return BoxSize.D;
        }
    }

    private getNewLockerState(newState: InboundCompartmentState) {
        switch (newState.toLowerCase()) {
            case 'damaged':
                return {
                    state: LockerMaintenanceStatus.MAINTENANCE,
                    reason: LockerMaintenanceStatusReason.COMPARTMENT_DAMAGED,
                };
            case 'dirty':
                return {
                    state: LockerMaintenanceStatus.MAINTENANCE,
                    reason: LockerMaintenanceStatusReason.DIRTY,
                };
            case 'inspection':
                return {
                    state: LockerMaintenanceStatus.TRIAGE,
                    reason: LockerMaintenanceStatusReason.OTHER,
                };
            case 'notdamaged':
            case 'notdirty':
            case 'notinspection':
            case '':
            default:
                return {
                    state: LockerMaintenanceStatus.NONE,
                    reason: LockerMaintenanceStatusReason.OTHER,
                };
        }
    }

    private getBoxMachineNameFromHeaders(req: HTTPRequest): string {
        return (
            (req.headers['boxMachineName'] as string) ?? (req.headers['boxmachinename'] as string)
        );
    }

    private async createRent(
        req: CreateParcelRequest,
        bloqData: BloqObjectResponse,
        res: HTTPResponse,
    ): Promise<CreateRentResponse> {
        const isLabelless = req.body.pickupWithoutLabel;
        const prePickupActions: PrePickupAction[] = [];

        if (isLabelless) {
            prePickupActions.push({
                order: 0,
                action: PrePickupActionType.ASSOCIATE_LABEL,
            });
        }

        const rentRequest = {
            externalID: req.body.parcelCode,
            pickUpCodes: {
                pin: req.body.openCode,
            },
            dropOffCode: req.body.carrierReference,
            customer: {
                phone: req.body.customerPhone,
            },
            prePickupActions,
            postPickupAction: [],
            setID: req.body.carrierReference,
            priority: isLabelless ? 1 : 0,
            desiredLockerType: getLockerTypeFromBoxSize(req.body.size),
        };

        const rent = await this.bloqitClient.createRent({
            rent: rentRequest,
            bloqId: bloqData._id,
        });

        return rent;
    }

    private async expireRent(rentId: string): Promise<UpdateRentResponse> {
        const fifteenSecondsFromNow = new Date(new Date().getTime() + 15 * 1000);

        const rent = await this.bloqitClient.updateRent({
            rentId,
            rent: {
                expiryDate: fifteenSecondsFromNow,
            },
        });

        return rent;
    }

    private async getLockerTitleFromBoxName(
        boxName: string,
        bloqExternalId: string,
    ): Promise<string> {
        const boxes = await this.mappedLockerRepository.retrieveBySharedId({
            sharedId: bloqExternalId,
        });

        if (boxes === undefined || boxes.layout.length === 0) {
            this.logger.error({
                message: `No mapped lockers found for bloqExternalID: ${bloqExternalId}`,
                data: { bloqExternalID: bloqExternalId },
            });
            return '';
        }

        const box = boxes.layout.find(box => box.partnerName === boxName);

        if (!box) {
            this.logger.error({
                message: `No box found for boxName: ${boxName}`,
                data: { boxName },
            });
            return '';
        }

        return box.lockerTitle;
    }

    private async getAllLockersFromBloq(bloqExternalId: string): Promise<string[]> {
        const bloqMappedLayout = await this.mappedLockerRepository.retrieveBySharedId({
            sharedId: bloqExternalId,
        });

        if (bloqMappedLayout === undefined || bloqMappedLayout.layout.length === 0) {
            this.logger.error({
                message: `No mapped lockers found for bloqExternalID: ${bloqExternalId}`,
                data: { bloqExternalID: bloqExternalId },
            });
            return [];
        }

        const lockers = bloqMappedLayout.layout.map(box => box.lockerTitle);

        if (!lockers || lockers.length === 0) {
            this.logger.error({
                message: `No mapped lockers found for bloqExternalID: ${bloqExternalId}`,
                data: { bloqExternalID: bloqExternalId },
            });
            return [];
        }

        return lockers;
    }

    private async getBoxNameFromLockerTitle(
        lockerTitle: string,
        bloqExternalId: string,
    ): Promise<string> {
        const boxes = await this.mappedLockerRepository.retrieveBySharedId({
            sharedId: bloqExternalId,
        });

        if (boxes === undefined || boxes.layout.length === 0) {
            this.logger.error({
                message: `No mapped lockers found for bloqExternalID: ${bloqExternalId}`,
                data: { bloqExternalID: bloqExternalId },
            });
            return '';
        }

        const box = boxes.layout.find(box => box.lockerTitle === lockerTitle);

        if (!box) {
            this.logger.error({
                message: `No locker found for lockerTitle: ${lockerTitle}`,
                data: { lockerTitle: lockerTitle },
            });
            return '';
        }

        return box.partnerName;
    }
}
