import { FakeBloqitClient } from '../../../bloqit/sdk/fake';
import { FakeLogger } from '../../../core/logger/fake';
import FakeExpressRequestBuilder from '../../../../test/mocks/http/express/request';
import FakeExpressResponseBuilder from '../../../../test/mocks/http/express/response';
import { InPostInboundController } from '.';
import * as httpCodes from '../../../http/models/codes';
import { simplifyControllerResponse } from '../../../../test/utils';
import { BoxMachineParcelStatus, BoxSize, type CompartmentInfo } from '../../models';
import { LockerType } from '../../../bloqit/lockers/models';
import { type MachineBlockRequestBody } from '../dto/requests/machine-block-request-body';
import { Status } from '../dto/responses/inpost-base-response';
import { MaintenanceStatus } from '../../../bloqit/bloqs/models';
import { Rent, RentState, UpdateRentResponse } from '../../../bloqit/rents/models/rent';
import { FakeMappedLockerRepository } from '../../data-access/repositories/mapped-locker/fake/fake-mapped-locker-repository';

describe('InPostInboundController', () => {
    let bloqitClient: FakeBloqitClient;
    let logger: FakeLogger;
    let mappedLockerRepository: FakeMappedLockerRepository;

    beforeEach(() => {
        logger = new FakeLogger();
        bloqitClient = new FakeBloqitClient();
        mappedLockerRepository = new FakeMappedLockerRepository();

        jest.spyOn(bloqitClient, 'activateBloq').mockResolvedValueOnce(undefined);
        jest.spyOn(bloqitClient, 'deactivateBloq').mockResolvedValueOnce(undefined);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('healthcheck', () => {
        it('returns 200 OK with a healthy status', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [],
            });

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const rawCtrlResp = await ctrl.healthcheck(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.OK);
        });

        it('returns 404 error if the machine is not found', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockRejectedValue(
                new Error('Machine not found'),
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const rawCtrlResp = await ctrl.healthcheck(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.BAD_REQUEST);
        });

        it('returns 404 error if the machine is inactive', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: false,
                lockers: [],
            });

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const rawCtrlResp = await ctrl.healthcheck(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.BAD_REQUEST);
        });
    });

    describe('getCompartmentInfo', () => {
        it('returns compartment info for a given box machine', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .build();

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const compartmentInfo: CompartmentInfo[] = [
                {
                    name: '1R1',
                    isOpened: true,
                    isBusy: false,
                    parcelInfo: [],
                    size: BoxSize.A,
                    state: 'Efficient',
                },
                {
                    name: '1R2',
                    isOpened: false,
                    isBusy: true,
                    parcelInfo: [
                        {
                            code: 'parcel-1',
                            size: BoxSize.B,
                            state: BoxMachineParcelStatus.PLACED_BY_COURIER,
                        },
                    ],
                    size: BoxSize.B,
                    state: 'Efficient',
                },
            ];

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                    {
                        _id: 'compartment-2',
                        rent: 'parcel-1',
                        isOpen: false,
                        lockerTitle: 'compartment-2',
                        type: LockerType.M,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'getRent').mockResolvedValue({
                _id: 'parcel-1',
                state: 'in_progress',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-2',
                        partnerName: '1R2',
                        size: LockerType.M,
                    },
                ],
            });

            const rawCtrlResp = await ctrl.getCompartmentInfo(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body).toEqual({ compartmentInfo });
        });
    });

    describe('Machine Block', () => {
        const boxMachineName = 'box-machine-name';
        const bloqByExternalIdData = {
            _id: 'bloq-1',
            active: true,
            lockers: [
                {
                    _id: 'compartment-1',
                    rent: null,
                    isOpen: true,
                    lockerTitle: 'compartment-1',
                    type: LockerType.S,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
                {
                    _id: 'compartment-2',
                    rent: 'parcel-1',
                    isOpen: false,
                    lockerTitle: 'compartment-2',
                    type: LockerType.M,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
            ],
        };

        it('should return 200 if it is a successful block', async () => {
            const requestBody: MachineBlockRequestBody = {
                block: true,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.toggleMachineBlock(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.deactivateBloq).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
            expect(response.body.statusMessage).toEqual(
                `Machine ${boxMachineName} has been blocked`,
            );
        });

        it('should return 200 if it is a successful unblock', async () => {
            const requestBody: MachineBlockRequestBody = {
                block: false,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.toggleMachineBlock(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.activateBloq).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
            expect(response.body.statusMessage).toEqual(
                `Machine ${boxMachineName} has been unblocked`,
            );
        });

        it('should fail if the machine name was not found', async () => {
            const requestBody: MachineBlockRequestBody = {
                block: false,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockRejectedValue(
                new Error('Bloq id error'),
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.toggleMachineBlock(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.activateBloq).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
            expect(response.body.statusMessage).toEqual(`No matching box machine was found`);
        });
    });

    describe('Open Compartments', () => {
        const bloqByExternalIdData = {
            _id: 'bloq-1',
            active: true,
            lockers: [
                {
                    _id: 'compartment-1',
                    rent: null,
                    isOpen: true,
                    lockerTitle: 'compartment-1',
                    type: LockerType.S,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
                {
                    _id: 'compartment-2',
                    rent: 'parcel-1',
                    isOpen: false,
                    lockerTitle: 'compartment-2',
                    type: LockerType.M,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
            ],
        };

        it('should return 200 if it is a successful open compartments', async () => {
            const requestBody = {
                boxList: ['1R1', '1R2'],
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'openDoors').mockResolvedValue({
                status: httpCodes.OK,
                lockers: ['compartment-1', 'compartment-2'],
            });

            jest.spyOn(bloqitClient, 'openDoorWithRent').mockResolvedValue({
                _id: 'parcel-1',
                locker: 'compartment-2',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id-1',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id-2',
                        lockerTitle: 'compartment-2',
                        partnerName: '1R2',
                        size: LockerType.M,
                    },
                ],
            });
            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.openCompartments(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.openDoors).toHaveBeenCalledTimes(1);
            expect(bloqitClient.openDoorWithRent).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
            expect(response.body.payload.boxlist.length).toEqual(2);
            expect(response.body.payload.boxlist).toEqual([
                { boxName: 'compartment-1', status: 'opened' },
                { boxName: 'compartment-2', status: 'opened' },
            ]);
        });

        it('should return 200 if it is a successful open compartments with 1 compartment', async () => {
            const requestBody = {
                boxList: ['1R1'],
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'openDoors').mockResolvedValue({
                status: httpCodes.OK,
                lockers: ['compartment-1'],
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.openCompartments(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.openDoors).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
            expect(response.body.payload.boxlist.length).toEqual(1);
        });

        it('should return 200 if it is a successful open compartments with ALL with 2 compartments', async () => {
            const requestBody = {
                boxList: ['ALL'],
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'openDoors').mockResolvedValue({
                status: httpCodes.OK,
                lockers: ['compartment-1', 'compartment-2'],
            });

            jest.spyOn(bloqitClient, 'openDoorWithRent').mockResolvedValue({
                _id: 'parcel-1',
                locker: 'compartment-2',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                    {
                        column: 1,
                        row: 2,
                        lockerId: 'locker-id-2',
                        lockerTitle: 'compartment-2',
                        partnerName: '1R2',
                        size: LockerType.M,
                    },
                ],
            });
            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.openCompartments(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.openDoors).toHaveBeenCalledTimes(1);
            expect(bloqitClient.openDoorWithRent).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
            expect(response.body.payload.boxlist.length).toEqual(2);
        });

        it('should return 200 if it receives a locker already opened error from core', async () => {
            const requestBody = {
                boxList: ['1R1'],
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'openDoors').mockRejectedValue({
                code: httpCodes.CONFLICT,
                message: 'Request failed with status code 409',
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                ],
            });
            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.openCompartments(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.openDoors).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });
    });

    describe('Create Parcel', () => {
        const bloqByExternalIdData = {
            _id: 'bloq-1',
            active: true,
            lockers: [
                {
                    _id: 'compartment-1',
                    rent: null,
                    isOpen: true,
                    lockerTitle: 'compartment-1',
                    type: LockerType.S,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
                {
                    _id: 'compartment-2',
                    rent: 'parcel-1',
                    isOpen: false,
                    lockerTitle: 'compartment-2',
                    type: LockerType.M,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
            ],
        };

        const createdRentData = {
            _id: 'rent-1',
            type: 'delivery',
            state: 'in_progress',
            states: [],
            customer: 'customer-1',
            locker: 'compartment-1',
            startDate: '2023-10-01T00:00:00Z',
            expiryDate: '2023-10-02T00:00:00Z',
            details: {
                pickUpCodes: {
                    pin: '1234',
                },
                dropOffCode: '5678',
            },
            pricing: {
                totalPrice: 10,
                openingPrice: 5,
                perMinutePrice: 0.1,
                paid: true,
                debt: 0,
                amountPaid: 10,
                discount: 0,
                durationMinutes: 60,
            },
            notificationOptions: ['email', 'sms'],
            desiredLockerType: LockerType.S,
        };

        it('should return 200 if it is a successful create parcel', async () => {
            const requestBody = {
                boxMachine: 'box-machine',
                carrier: 'carrier',
                carrierReference: 'carrier-reference',
                customerPhone: 'customer-phone',
                directParcel: true,
                endOfWeekCollection: false,
                multiCompPotentialSize: 'multi-comp-potential-size',
                multiCompPotentialUuid: 'multi-comp-potential-uuid',
                onDeliveryAmount: 100,
                openCode: 'open-code',
                parcelAttributes: [
                    {
                        name: 'attribute-name',
                        value: 'attribute-value',
                    },
                ],
                parcelCode: 'parcel-code',
                payCode: 1,
                pickupWithoutLabel: false,
                senderBoxMachine: 'sender-box-machine',
                size: BoxSize.B,
                status: BoxMachineParcelStatus.PLACED_BY_COURIER,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'createRent').mockResolvedValue({
                createdRentData,
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockRejectedValue({
                status: httpCodes.NOT_FOUND,
                message: 'Rent not found',
            });

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.createOrUpdateParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.createRent).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });
    });

    describe('Update Parcel', () => {
        const bloqByExternalIdData = {
            _id: 'bloq-1',
            active: true,
            lockers: [
                {
                    _id: 'compartment-1',
                    rent: null,
                    isOpen: true,
                    lockerTitle: 'compartment-1',
                    type: LockerType.S,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
                {
                    _id: 'compartment-2',
                    rent: 'parcel-1',
                    isOpen: false,
                    lockerTitle: 'compartment-2',
                    type: LockerType.M,
                    active: true,
                    createdAt: new Date().toDateString(),
                    updatedAt: new Date().toDateString(),
                },
            ],
        };

        const updatedRentData: UpdateRentResponse = {
            docs: [
                {
                    externalID: 'parcel-1',
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                } as Rent,
            ],
            limit: 0,
            page: 0,
            totalDocs: 0,
        };

        const existingRentData = {
            _id: 'parcel-id',
            state: 'in_progress',
            externalID: 'parcel-external-id',
            bloq: {
                _id: 'bloq-id',
                active: true,
                lockers: [],
            },
            details: {
                pickUpCodes: {
                    pin: '1234',
                },
                dropOffCode: '5678',
            },
            locker: {
                _id: 'locker-id',
                lockerTitle: 'locker-title',
                type: LockerType.M,
                bloq: {
                    _id: 'bloq-id',
                },
            },
        };

        it('should return 200 if it is a successful update parcel', async () => {
            const requestBody = {
                boxMachine: 'box-machine',
                carrier: 'carrier',
                carrierReference: 'carrier-reference',
                customerPhone: 'customer-phone',
                directParcel: true,
                endOfWeekCollection: false,
                multiCompPotentialSize: 'multi-comp-potential-size',
                multiCompPotentialUuid: 'multi-comp-potential-uuid',
                onDeliveryAmount: 100,
                openCode: 'open-code',
                parcelAttributes: [
                    {
                        name: 'attribute-name',
                        value: 'attribute-value',
                    },
                ],
                parcelCode: 'parcel-code',
                payCode: 1,
                pickupWithoutLabel: false,
                senderBoxMachine: 'sender-box-machine',
                size: BoxSize.B,
                status: BoxMachineParcelStatus.PLACED_BY_COURIER,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'updateRent').mockResolvedValue(updatedRentData);

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue(
                existingRentData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.createOrUpdateParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.updateRent).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should return 200 if rent is set to expired', async () => {
            const requestBody = {
                boxMachine: 'box-machine',
                carrier: 'carrier',
                carrierReference: 'carrier-reference',
                customerPhone: 'customer-phone',
                directParcel: true,
                endOfWeekCollection: false,
                multiCompPotentialSize: 'multi-comp-potential-size',
                multiCompPotentialUuid: 'multi-comp-potential-uuid',
                onDeliveryAmount: 100,
                openCode: 'open-code',
                parcelAttributes: [
                    {
                        name: 'attribute-name',
                        value: 'attribute-value',
                    },
                ],
                parcelCode: 'parcel-code',
                payCode: 1,
                pickupWithoutLabel: false,
                senderBoxMachine: 'sender-box-machine',
                size: BoxSize.B,
                status: BoxMachineParcelStatus.EXPIRED,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'updateRent').mockResolvedValue(updatedRentData);

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue(
                existingRentData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.createOrUpdateParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.updateRent).toHaveBeenCalledTimes(2);
            expect(bloqitClient.updateRent).toHaveBeenCalledWith({
                rentId: 'parcel-id',
                rent: {
                    expiryDate: expect.any(Date),
                },
            });
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should return 200 if rent is set to claimed', async () => {
            const requestBody = {
                boxMachine: 'box-machine',
                carrier: 'carrier',
                carrierReference: 'carrier-reference',
                customerPhone: 'customer-phone',
                directParcel: true,
                endOfWeekCollection: false,
                multiCompPotentialSize: 'multi-comp-potential-size',
                multiCompPotentialUuid: 'multi-comp-potential-uuid',
                onDeliveryAmount: 100,
                openCode: 'open-code',
                parcelAttributes: [
                    {
                        name: 'attribute-name',
                        value: 'attribute-value',
                    },
                ],
                parcelCode: 'parcel-code',
                payCode: 1,
                pickupWithoutLabel: false,
                senderBoxMachine: 'sender-box-machine',
                size: BoxSize.B,
                status: BoxMachineParcelStatus.CLAIMED,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'updateRent').mockResolvedValue(updatedRentData);
            jest.spyOn(bloqitClient, 'cancelRent').mockResolvedValue(updatedRentData);

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue(
                existingRentData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.createOrUpdateParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.updateRent).toHaveBeenCalledTimes(1);
            expect(bloqitClient.cancelRent).toHaveBeenCalledTimes(1);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should return 200 if rent is set to not available', async () => {
            const requestBody = {
                boxMachine: 'box-machine',
                carrier: 'carrier',
                carrierReference: 'carrier-reference',
                customerPhone: 'customer-phone',
                directParcel: true,
                endOfWeekCollection: false,
                multiCompPotentialSize: 'multi-comp-potential-size',
                multiCompPotentialUuid: 'multi-comp-potential-uuid',
                onDeliveryAmount: 100,
                openCode: 'open-code',
                parcelAttributes: [
                    {
                        name: 'attribute-name',
                        value: 'attribute-value',
                    },
                ],
                parcelCode: 'parcel-code',
                payCode: 1,
                pickupWithoutLabel: false,
                senderBoxMachine: 'sender-box-machine',
                size: BoxSize.B,
                status: BoxMachineParcelStatus.NOT_AVAILABLE,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'updateRent').mockResolvedValue(updatedRentData);
            jest.spyOn(bloqitClient, 'cancelRent').mockResolvedValue(updatedRentData);
            jest.spyOn(bloqitClient, 'finishRent').mockResolvedValue(updatedRentData);

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue(
                existingRentData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.createOrUpdateParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.updateRent).toHaveBeenCalledTimes(1);
            expect(bloqitClient.cancelRent).toHaveBeenCalledTimes(1);
            expect(bloqitClient.finishRent).toHaveBeenCalledTimes(1);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should return 200 without cancel and finish if rent is set to not available and it was already finished', async () => {
            const requestBody = {
                boxMachine: 'box-machine',
                carrier: 'carrier',
                carrierReference: 'carrier-reference',
                customerPhone: 'customer-phone',
                directParcel: true,
                endOfWeekCollection: false,
                multiCompPotentialSize: 'multi-comp-potential-size',
                multiCompPotentialUuid: 'multi-comp-potential-uuid',
                onDeliveryAmount: 100,
                openCode: 'open-code',
                parcelAttributes: [
                    {
                        name: 'attribute-name',
                        value: 'attribute-value',
                    },
                ],
                parcelCode: 'parcel-code',
                payCode: 1,
                pickupWithoutLabel: false,
                senderBoxMachine: 'sender-box-machine',
                size: BoxSize.B,
                status: BoxMachineParcelStatus.NOT_AVAILABLE,
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue(
                bloqByExternalIdData,
            );

            jest.spyOn(bloqitClient, 'updateRent').mockResolvedValue(updatedRentData);
            jest.spyOn(bloqitClient, 'cancelRent').mockResolvedValue(updatedRentData);
            jest.spyOn(bloqitClient, 'finishRent').mockResolvedValue(updatedRentData);

            const finishedRentData = existingRentData;
            finishedRentData.state = RentState.FINISHED;

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue(
                finishedRentData,
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.createOrUpdateParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.updateRent).toHaveBeenCalledTimes(1);
            expect(bloqitClient.cancelRent).not.toHaveBeenCalled();
            expect(bloqitClient.finishRent).not.toHaveBeenCalled();

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });
    });

    describe('Deliver Parcel', () => {
        it('should return 200 if it is a successful delivery', async () => {
            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withParams({ parcelCode: 'parcel-1' })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
                state: 'in_progress',
                externalID: 'parcel-1',
                bloq: 'bloq-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
                locker: {
                    _id: 'compartment-1',
                    bloq: {
                        _id: 'bloq-1',
                    },
                },
            });

            jest.spyOn(bloqitClient, 'collectItem').mockResolvedValue({
                _id: 'parcel-1',
                state: 'in_progress',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
            });

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deliverParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.collectItem).toHaveBeenCalledTimes(1);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should fail if the machine name was not found', async () => {
            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withParams({ parcelCode: 'parcel-1' })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockRejectedValue(
                new Error('Bloq id error'),
            );

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
            });

            jest.spyOn(bloqitClient, 'collectItem').mockResolvedValueOnce(undefined);

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deliverParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.collectItem).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });

        it('should fail if the rent is in a different bloq', async () => {
            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withParams({ parcelCode: 'parcel-1' })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
                state: 'in_progress',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
                locker: {
                    _id: 'compartment-2',
                    bloq: {
                        _id: 'bloq-2',
                    },
                },
            });

            jest.spyOn(bloqitClient, 'collectItem').mockResolvedValueOnce(undefined);

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deliverParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.collectItem).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });

        it('should fail if the rent is not in progress, cancelled or expired', async () => {
            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withParams({ parcelCode: 'parcel-1' })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
                state: 'finished',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
                locker: {
                    _id: 'compartment-1',
                    bloq: {
                        _id: 'bloq-1',
                    },
                },
            });

            jest.spyOn(bloqitClient, 'collectItem').mockResolvedValueOnce(undefined);

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deliverParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.collectItem).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });

        it('should fail if the parcel was not found', async () => {
            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withParams({ parcelCode: 'parcel-1' })
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockRejectedValue(
                new Error('Rent id error'),
            );

            jest.spyOn(bloqitClient, 'collectItem').mockResolvedValueOnce(undefined);

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deliverParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.collectItem).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });
    });

    describe('Delete Parcel', () => {
        it('delete a parcel successfully', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody({ packCode: 'parcel-1' })
                .build();

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
                state: 'in_progress',
                externalID: 'parcel-1',
                bloq: 'bloq-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
                locker: {
                    _id: 'compartment-1',
                    bloq: {
                        _id: 'bloq-1',
                    },
                },
            });

            jest.spyOn(bloqitClient, 'cancelRent').mockResolvedValue({
                _id: 'parcel-1',
                state: 'cancelled',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
            });
            jest.spyOn(bloqitClient, 'finishRent').mockResolvedValue({
                _id: 'parcel-1',
                state: 'finished',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
            });

            const rawCtrlResp = await ctrl.deleteParcel(req, res);
            const response = simplifyControllerResponse(rawCtrlResp);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should fail if the machine name was not found', async () => {
            const requestBody = {
                packCode: 'parcel-1',
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockRejectedValue(
                new Error('Bloq id error'),
            );

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
            });

            jest.spyOn(bloqitClient, 'cancelRent').mockResolvedValueOnce(undefined);
            jest.spyOn(bloqitClient, 'finishRent').mockResolvedValueOnce(undefined);

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deleteParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.cancelRent).toHaveBeenCalledTimes(0);
            expect(bloqitClient.finishRent).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });

        it('should fail if the rent is not in progress', async () => {
            const requestBody = {
                packCode: 'parcel-1',
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'retrieveRentByExternalId').mockResolvedValue({
                _id: 'parcel-1',
                state: 'finished',
                externalID: 'parcel-1',
                details: {
                    pickUpCodes: {
                        pin: '1234',
                    },
                    dropOffCode: '5678',
                },
                locker: {
                    _id: 'compartment-1',
                    bloq: {
                        _id: 'bloq-1',
                    },
                },
            });

            jest.spyOn(bloqitClient, 'cancelRent').mockResolvedValueOnce(undefined);
            jest.spyOn(bloqitClient, 'finishRent').mockResolvedValueOnce(undefined);

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.deleteParcel(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.cancelRent).toHaveBeenCalledTimes(0);
            expect(bloqitClient.finishRent).toHaveBeenCalledTimes(0);
            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });
    });

    describe('Update Compartment', () => {
        it('should return 200 if it is a successful update compartment', async () => {
            const requestBody = {
                boxes: ['1R1'],
                state: 'DIRTY',
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                        maintenanceStatus: MaintenanceStatus.NONE,
                    },
                    {
                        _id: 'compartment-2',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-2',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                        maintenanceStatus: MaintenanceStatus.NONE,
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'reportLockerProblem').mockResolvedValue({
                _id: 'compartment-1',
                rent: null,
                isOpen: true,
                lockerTitle: 'compartment-1',
                type: LockerType.S,
                active: true,
                createdAt: new Date().toDateString(),
                updatedAt: new Date().toDateString(),
                maintenanceStatus: MaintenanceStatus.MAINTENANCE,
                maintenanceStatusReason: 'dirty',
            });

            jest.spyOn(bloqitClient, 'changeLockerAvailability').mockResolvedValue({
                _id: 'compartment-1',
                rent: null,
                isOpen: true,
                lockerTitle: 'compartment-1',
                type: LockerType.S,
                active: false,
                createdAt: new Date().toDateString(),
                updatedAt: new Date().toDateString(),
                maintenanceStatus: MaintenanceStatus.MAINTENANCE,
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-2',
                        partnerName: '1R2',
                        size: LockerType.M,
                    },
                ],
            });

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.updateCompartment(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.reportLockerProblem).toHaveBeenCalledTimes(1);
            expect(bloqitClient.changeLockerAvailability).toHaveBeenCalledTimes(1);
            expect(bloqitClient.changeLockerAvailability).toHaveBeenCalledWith({
                bloqId: 'bloq-1',
                lockerId: 'compartment-1',
                active: false,
            });
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.status).toEqual(Status.OK);
        });

        it('should return 200 and report failures if only some of the compartment updates succeed', async () => {
            const requestBody = {
                boxes: ['1R1', '1R2'],
                state: 'DIRTY',
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                        maintenanceStatus: MaintenanceStatus.NONE,
                    },
                    {
                        _id: 'compartment-2',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-2',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                        maintenanceStatus: MaintenanceStatus.NONE,
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'reportLockerProblem').mockResolvedValueOnce({
                _id: 'compartment-1',
                rent: null,
                isOpen: true,
                lockerTitle: 'compartment-1',
                type: LockerType.S,
                active: true,
                createdAt: new Date().toDateString(),
                updatedAt: new Date().toDateString(),
                maintenanceStatus: MaintenanceStatus.MAINTENANCE,
                maintenanceStatusReason: 'dirty',
            });

            jest.spyOn(bloqitClient, 'changeLockerAvailability').mockResolvedValue({
                _id: 'compartment-1',
                rent: null,
                isOpen: true,
                lockerTitle: 'compartment-1',
                type: LockerType.S,
                active: false,
                createdAt: new Date().toDateString(),
                updatedAt: new Date().toDateString(),
                maintenanceStatus: MaintenanceStatus.MAINTENANCE,
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-2',
                        partnerName: '1R2',
                        size: LockerType.M,
                    },
                ],
            });
            jest.spyOn(bloqitClient, 'reportLockerProblem').mockRejectedValueOnce(
                new Error('Update error'),
            );

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.updateCompartment(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.reportLockerProblem).toHaveBeenCalledTimes(2);
            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.payload.updated.length).toEqual(1);
            expect(response.body.payload.updated[0]).toEqual('compartment-1');
            expect(response.body.payload.failed.length).toEqual(1);
            expect(response.body.payload.failed[0].compartment).toEqual('compartment-2');
            expect(response.body.payload.failed[0].failReason).toEqual(new Error('Update error'));
        });

        it('should return 500 if all updates fail', async () => {
            const requestBody = {
                boxes: ['1R1', '1R2'],
                state: 'DIRTY',
            };

            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxmachinename: 'box-machine-name',
                })
                .withBody(requestBody)
                .build();

            jest.spyOn(bloqitClient, 'retrieveBloqByExternalId').mockResolvedValue({
                _id: 'bloq-1',
                active: true,
                lockers: [
                    {
                        _id: 'compartment-1',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-1',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                        maintenanceStatus: MaintenanceStatus.NONE,
                    },
                    {
                        _id: 'compartment-2',
                        rent: null,
                        isOpen: true,
                        lockerTitle: 'compartment-2',
                        type: LockerType.S,
                        active: true,
                        createdAt: new Date().toDateString(),
                        updatedAt: new Date().toDateString(),
                        maintenanceStatus: MaintenanceStatus.NONE,
                    },
                ],
            });

            jest.spyOn(bloqitClient, 'reportLockerProblem').mockRejectedValueOnce(
                new Error('Update error'),
            );

            jest.spyOn(bloqitClient, 'changeLockerAvailability').mockResolvedValue({
                _id: 'compartment-1',
                rent: null,
                isOpen: true,
                lockerTitle: 'compartment-1',
                type: LockerType.S,
                active: false,
                createdAt: new Date().toDateString(),
                updatedAt: new Date().toDateString(),
                maintenanceStatus: MaintenanceStatus.MAINTENANCE,
            });

            jest.spyOn(mappedLockerRepository, 'retrieveBySharedId').mockResolvedValue({
                bloqId: 'bloq-1',
                sharedId: 'shared-1',
                layout: [
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-1',
                        partnerName: '1R1',
                        size: LockerType.M,
                    },
                    {
                        column: 1,
                        row: 1,
                        lockerId: 'locker-id',
                        lockerTitle: 'compartment-2',
                        partnerName: '1R2',
                        size: LockerType.M,
                    },
                ],
            });
            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.updateCompartment(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(bloqitClient.reportLockerProblem).toHaveBeenCalledTimes(2);
            expect(response.status).toBe(httpCodes.INTERNAL_SERVER_ERROR);
            expect(response.body.status).toEqual(Status.ERROR);
        });
    });

    describe('Get Proximity Code', () => {
        it('Should return OK with proximity code if the boxMachineName is provided', async () => {
            const res = new FakeExpressResponseBuilder().build();
            const req = new FakeExpressRequestBuilder()
                .withHeaders({
                    'Content-Type': 'application/json',
                    boxMachineName: 'box-machine-name',
                })
                .build();

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.getProximityCode(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(response.status).toBe(httpCodes.OK);
            expect(response.body.proximityCode).toBe('1688');
        });

        it('Should return BAD_REQUEST if the boxMachineName is not provided', async () => {
            const res = new FakeExpressResponseBuilder().build();

            const req = new FakeExpressRequestBuilder()
                .withHeaders({ 'Content-Type': 'application/json' })
                .withParams({})
                .build();

            const ctrl = new InPostInboundController({
                bloqitClient,
                logger,
                mappedLockerRepository,
            });

            const responseObj = await ctrl.getProximityCode(req, res);
            const response = simplifyControllerResponse(responseObj);

            expect(response.status).toBe(httpCodes.BAD_REQUEST);
            expect(response.body.status).toEqual(Status.ERROR);
        });
    });
});
