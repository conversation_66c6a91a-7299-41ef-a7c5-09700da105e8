import { type DHLCZDataResponse } from '../dataResponse';
import { type DHLCZDataAuthenticate } from '../dataAuthenticate';

/**
 * @description Payload containing the response from DHL to the Authenticate call
 * @property {DHLCZDataResponse} dataResponse - Status information about the authentication request
 * @property {DHLCZDataAuthenticate} dataAuthenticate - Courier permission details if authentication was successful
 */
export interface AuthenticateResponse {
    dataResponse: DHLCZDataResponse;
    dataAuthenticate: DHLCZDataAuthenticate;
}
