import { DHL_CZClientImpl, GENERIC_GET_SHIPMENT_ERROR_MSG } from '.';
import { FakeHTTPClient } from '../../../../core/http-client/fake';
import { Command } from '../../models/dataConfig';
import {
    BusinessType,
    OperationType,
    ApplicationLocale,
    AccessRole,
    ExternalPartner,
} from '../../models/dataRequest';
import { Status } from '../../models/dataResponse';
import {
    type DHLCZDataElement,
    DHLCZCountryCodes,
    DHLCZEventCodes,
    DHLCZLocationCodes,
    ProductType as DataElementProductType,
} from '../../models/dataElement';
import {
    ProductType as GetShipmentProductType,
    type GetShipmentResponse,
} from '../../models/get-shipment-response';
import { PartnerResponseError } from '../../../../core/utils/error';
import * as httpCodes from '../../../../http/models/codes';
import { type ApplicationEnvironment } from '../../../../core/env';

const MOCKED_DHL_GET_SHIPMENT_RESPONSE: GetShipmentResponse = {
    dataResponse: {
        status: Status.OK,
        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
        statusMessage: 'Request accepted',
        timestamp: '2019-10-18T14:04:05.185+02:0',
        statusCode: '200',
    },
    dataElement: {
        version: '0200',
        dataElementType: 'xPAN',
        parcelOriginOrganization: 'CZ-7001',
        parcelDestinationOrganization: 'CZ-7001',
        dataElementOriginOrganization: 'CZ-7001',
        distributionTimestamp: '2019-10-18T13:11:43',
        payment: { paymentStatus: 'paid' },
        general: {
            product: GetShipmentProductType.ParcelConnect,
            parcelIdentifier: ['********'],
            routingCode: '2LCZ35002+********',
            timestamp: '2019-10-18T13:11:43',
        },
        xPAN: {
            addresses: {
                sender: [{ type: 'sender', email: '<EMAIL>', name: 'John Doe' }],
                recipient: [{ type: 'parcelbox', email: '<EMAIL>', name: 'Jane Doe' }],
            },
            features: {
                cod: { nonSepa: { amount: '607.0', currency: 'CZK' } },
                physical: { grossWeight: '7.11', length: '10', width: '20', height: '30' },
                identityCheck: {
                    trueOrFalse: 'true',
                    typeOfDocument: 'PIN',
                    documentIdentifcationNr: '123456',
                },
            },
            featureCodes: {
                feature: [
                    { name: ['SmartPIN'], textValue: ['ABC123'] },
                    { name: ['LabelLess'], textValue: ['true'] },
                    { name: ['BiggestPsSize'], textValue: ['XXL'] },
                ],
            },
        },
    },
};

const env = {
    DHL_CZ_OAUTH_HOST: 'https://authservice-test.ppl.cz',
    DHL_CZ_API_HOST: 'https://api-test.ppl.cz',
    DHL_CZ_OAUTH_CLIENT_ID: 'dummy-client-id',
    DHL_CZ_OAUTH_CLIENT_SECRET: 'dummy-client-secret',
} as unknown as ApplicationEnvironment;

describe('DHL_CZClientImpl', () => {
    describe('getAccessToken', () => {
        it('should retrieve an access token from DHL CZ', async () => {
            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    access_token: 'dummy-dhl-cz-access-token',
                    expires_in: 123,
                    refresh_expires_in: 456,
                    token_type: 'dummy-token-type',
                    'not-before-policy': 789,
                    scope: 'dummy-scope',
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });

            const token = await client.getAccessToken();

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_OAUTH_HOST}/auth/realms/HIPP_ext_auth/protocol/openid-connect/token`,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    Host: 'authservice-test.ppl.cz',
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    client_id: env.DHL_CZ_OAUTH_CLIENT_ID,
                    client_secret: env.DHL_CZ_OAUTH_CLIENT_SECRET,
                    grant_type: 'client_credentials',
                    scopes: '[HIPP.AP]',
                },
            });

            expect(token.access_token).toEqual('dummy-dhl-cz-access-token');
            expect(token.expires_in).toEqual(123);
            expect(token.refresh_expires_in).toEqual(456);
            expect(token.token_type).toEqual('dummy-token-type');
            expect(token['not-before-policy']).toEqual(789);
            expect(token.scope).toEqual('dummy-scope');
        });
    });

    describe('setConfig', () => {
        it('should perform a call to DHL CZ to persist a set of configurations', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    sessionId: 'dummy-session-id',
                    statusMessage: 'dummy-status-message',
                    status: Status.OK,
                    timestamp: new Date().toUTCString(),
                    statusCode: '200',
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });

            const requestData = {
                externalPartner: 'Bloqit',
                businessType: BusinessType.PPL,
                operationType: OperationType.FIRST_MILE,
                accessPointId: 'dummy-access-point-id',
                slotId: '123',
                driverId: '456',
                parcelIdentifier: '789',
                sessionId: 'dummy-session-id',
                applicationLocale: ApplicationLocale.cz,
            };
            const requestConfig = {
                command: Command.activate,
                deviceId: '1234',
                gps: { Lat: -46.23432, Lon: -26.3242342 },
                posTerminal: { SN: 123, TID: 11 },
                address: 'Dummy address',
                parameters: { param1: 'value1' },
            };

            const response = await client.setConfig(
                { token: 'dummy-token', validity: new Date() },
                requestData,
                requestConfig,
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPointConfigure/v1/configure`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: { dataRequest: requestData, dataConfig: requestConfig },
            });

            expect(response).toEqual({
                sessionId: 'dummy-session-id',
                statusMessage: 'dummy-status-message',
                status: Status.OK,
                timestamp: new Date().toUTCString(),
                statusCode: '200',
            });
        });
    });

    describe('authenticateCourier', () => {
        it('should perform a call to DHL CZ to check if courier is authorized to login in bloq', async () => {
            const httpClient = new FakeHTTPClient();

            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    dataResponse: {
                        sessionId: 'dummy-session-id',
                        statusMessage: 'dummy-status-message',
                        status: Status.OK,
                        timestamp: new Date().toUTCString(),
                        statusCode: '200',
                    },
                    dataAuthenticate: {
                        dropOffAllowed: true,
                        pickUpAllowed: true,
                    },
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });

            const requestData = {
                externalPartner: 'Bloqit',
                businessType: BusinessType.PPL,
                accessPointId: 'dummy-access-point-id',
                driverId: '456',
                sessionId: 'dummy-session-id',
                accessRole: AccessRole.courier,
                applicationLocale: ApplicationLocale.cz,
            };

            const response = await client.authenticateCourier(
                { token: 'dummy-token', validity: new Date() },
                requestData,
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/authenticate`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: { dataRequest: requestData },
            });

            expect(response).toEqual({
                dataResponse: {
                    sessionId: 'dummy-session-id',
                    statusMessage: 'dummy-status-message',
                    status: Status.OK,
                    timestamp: new Date().toUTCString(),
                    statusCode: '200',
                },
                dataAuthenticate: {
                    dropOffAllowed: true,
                    pickUpAllowed: true,
                },
            });
        });
    });

    describe('notifyEvent', () => {
        const httpClient = new FakeHTTPClient();
        const client = new DHL_CZClientImpl({ httpClient, env });
        const mockResponseTimestamp = new Date().toUTCString();

        const event = {
            dataRequest: {
                externalPartner: 'Bloqit',
                businessType: BusinessType.PPL,
                accessPointId: 'dummy-access-point-id',
                driverId: '456',
                sessionId: 'dummy-session-id',
                accessRole: AccessRole.courier,
                applicationLocale: ApplicationLocale.cz,
            },
            dataElement: {
                version: '0200',
                dataElementType: 'xPAN',
                parcelOriginOrganization: 'CZ-7001',
                parcelDestinationOrganization: 'CZ-7001',
                dataElementOriginOrganization: 'CZ-7001',
                distributionTimestamp: new Date(),
                general: {
                    product: DataElementProductType.parcelConnect,
                    parcelIdentifier: ['dummy-external-id'],
                    routingCode: '2LCZ35002+********',
                    timestamp: new Date(),
                },
                event: {
                    parcelEuropeSpecificData: {
                        eventCode: DHLCZEventCodes.customerDelivery,
                        country: DHLCZCountryCodes.CZ,
                        location: DHLCZLocationCodes.parcelStation,
                        document: [
                            {
                                type: 'Note2',
                                reference: 'dummy-bloq-external-id',
                            },
                        ],
                    },
                },
            },
        };

        let requestSpy: jest.SpyInstance;

        afterEach(() => {
            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/event`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: event,
                validateStatus: expect.any(Function),
            });

            jest.restoreAllMocks();
        });

        it('should perform a call to DHL CZ to send an event', async () => {
            requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    dataResponse: {
                        sessionId: 'dummy-session-id',
                        statusMessage: 'dummy-status-message',
                        status: Status.OK,
                        timestamp: mockResponseTimestamp,
                        statusCode: '200',
                    },
                },
            });

            const response = await client.notifyEvent(
                { token: 'dummy-token', validity: new Date() },
                event,
            );

            expect(response).toEqual({
                sessionId: 'dummy-session-id',
                statusMessage: 'dummy-status-message',
                status: Status.OK,
                timestamp: mockResponseTimestamp,
                statusCode: '200',
            });
        });

        it('should throw a managed error and forward the DHL-baked, domain specific message accordingly', async () => {
            requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                data: {
                    dataResponse: {
                        sessionId: 'dummy-session-id',
                        statusMessage: 'dummy-status-message',
                        status: Status.ERROR,
                        timestamp: mockResponseTimestamp,
                        statusCode: '200',
                    },
                },
            });

            await expect(
                client.notifyEvent({ token: 'dummy-token', validity: new Date() }, event),
            ).rejects.toThrow(
                new PartnerResponseError({
                    code: 'dummy-status-code',
                    message: 'dummy-status-message',
                }),
            );
        });
    });

    describe('getShipment', () => {
        it('should retrieve a last-mile shipment from DHL API', async () => {
            const accessToken = { token: 'dummy-token', validity: new Date() };
            const accessPointId = 'dummy-access-point-id';
            const parcelIdentifier = 'dummy-parcel-id';
            const operationType = OperationType.LAST_MILE;
            const locale = 'dummy-locale';
            const driverId = 'dummy-driver-id';

            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: httpCodes.OK,
                data: MOCKED_DHL_GET_SHIPMENT_RESPONSE,
            });

            const client = new DHL_CZClientImpl({ httpClient, env });
            const receivedResponse = await client.getShipment({
                accessToken,
                parcelIdentifier,
                accessPointId,
                operationType,
                locale,
                driverId,
            });

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/shipment`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    dataRequest: {
                        parcelIdentifier,
                        accessPointId,
                        operationType,
                        externalPartner: ExternalPartner,
                        businessType: BusinessType.PPL,
                        sessionId: expect.any(String),
                        applicationLocale: locale,
                        driverId,
                    },
                },
                /*
                    That'd be great to have a firm validation like validateStatus: () => true here,
                    but it seems that jest gets lost in the equality comparisons if we add this. So
                    we're sticking to a more generic `expect.any(Function)` for now.
                */
                validateStatus: expect.any(Function),
            });

            expect(receivedResponse).toEqual(MOCKED_DHL_GET_SHIPMENT_RESPONSE);
        });

        it('should throw a managed error and forward the DHL-baked, domain specific message accordingly in case custom status response equals ERROR and HTTP code equals 200 OK and there is a status message', async () => {
            const accessToken = { token: 'dummy-token', validity: new Date() };
            const accessPointId = 'dummy-access-point-id';
            const parcelIdentifier = 'dummy-parcel-id';
            const operationType = OperationType.LAST_MILE;
            const locale = 'dummy-locale';
            const driverId = 'dummy-driver-id';

            const error = new PartnerResponseError({
                code: '600',
                message: 'DHL-baked, domain specific error message',
            });

            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: 200,
                data: {
                    dataResponse: {
                        status: Status.ERROR,
                        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
                        statusMessage: 'DHL-baked, domain specific error message',
                        timestamp: '2019-10-18T14:04:05.185+02:0',
                        statusCode: '600',
                    },
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });
            await expect(
                client.getShipment({
                    accessToken,
                    parcelIdentifier,
                    accessPointId,
                    operationType,
                    locale,
                    driverId,
                }),
            ).rejects.toThrow(error);

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/shipment`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    dataRequest: {
                        parcelIdentifier,
                        accessPointId,
                        operationType,
                        externalPartner: ExternalPartner,
                        businessType: BusinessType.PPL,
                        sessionId: expect.any(String),
                        applicationLocale: locale,
                        driverId,
                    },
                },
                /*
                    That'd be great to have a firm validation like validateStatus: () => true here,
                    but it seems that jest gets lost in the equality comparisons if we add this. So
                    we're sticking to a more generic `expect.any(Function)` for now.
                */
                validateStatus: expect.any(Function),
            });
        });

        it('should throw a managed error with a generic error message in case of custom status response equals ERROR and HTTP code equals 200 OK and the status message is empty', async () => {
            const accessToken = { token: 'dummy-token', validity: new Date() };
            const accessPointId = 'dummy-access-point-id';
            const parcelIdentifier = 'dummy-parcel-id';
            const operationType = OperationType.LAST_MILE;
            const locale = 'dummy-locale';
            const driverId = 'dummy-driver-id';

            const error = new PartnerResponseError({
                code: '600',
                message: GENERIC_GET_SHIPMENT_ERROR_MSG,
            });

            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: 200,
                data: {
                    dataResponse: {
                        status: Status.ERROR,
                        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
                        statusMessage: '',
                        timestamp: '2019-10-18T14:04:05.185+02:0',
                        statusCode: '600',
                    },
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });
            await expect(
                client.getShipment({
                    accessToken,
                    parcelIdentifier,
                    accessPointId,
                    operationType,
                    locale,
                    driverId,
                }),
            ).rejects.toThrow(error);

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/shipment`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    dataRequest: {
                        parcelIdentifier,
                        accessPointId,
                        operationType,
                        externalPartner: ExternalPartner,
                        businessType: BusinessType.PPL,
                        sessionId: expect.any(String),
                        applicationLocale: locale,
                        driverId,
                    },
                },
                /*
                    That'd be great to have a firm validation like validateStatus: () => true here,
                    but it seems that jest gets lost in the equality comparisons if we add this. So
                    we're sticking to a more generic `expect.any(Function)` for now.
                */
                validateStatus: expect.any(Function),
            });
        });

        it('should throw a managed error with a generic error message in case of custom status response equals ERROR and HTTP code equals 200 OK and there is no status message', async () => {
            const accessToken = { token: 'dummy-token', validity: new Date() };
            const accessPointId = 'dummy-access-point-id';
            const parcelIdentifier = 'dummy-parcel-id';
            const operationType = OperationType.LAST_MILE;
            const locale = 'dummy-locale';
            const driverId = 'dummy-driver-id';

            const error = new PartnerResponseError({
                code: '600',
                message: GENERIC_GET_SHIPMENT_ERROR_MSG,
            });

            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: 200,
                data: {
                    dataResponse: {
                        status: Status.ERROR,
                        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
                        timestamp: '2019-10-18T14:04:05.185+02:0',
                        statusCode: '600',
                    },
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });
            await expect(
                client.getShipment({
                    accessToken,
                    parcelIdentifier,
                    accessPointId,
                    operationType,
                    locale,
                    driverId,
                }),
            ).rejects.toThrow(error);

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/shipment`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    dataRequest: {
                        parcelIdentifier,
                        accessPointId,
                        operationType,
                        externalPartner: ExternalPartner,
                        businessType: BusinessType.PPL,
                        sessionId: expect.any(String),
                        applicationLocale: locale,
                        driverId,
                    },
                },
                /*
                    That'd be great to have a firm validation like validateStatus: () => true here,
                    but it seems that jest gets lost in the equality comparisons if we add this. So
                    we're sticking to a more generic `expect.any(Function)` for now.
                */
                validateStatus: expect.any(Function),
            });
        });

        it('should throw a managed error with a generic error message in case of failures with HTTP status codes different than 200 OK', async () => {
            const accessToken = { token: 'dummy-token', validity: new Date() };
            const accessPointId = 'dummy-access-point-id';
            const parcelIdentifier = 'dummy-parcel-id';
            const operationType = OperationType.LAST_MILE;
            const locale = 'dummy-locale';
            const driverId = 'dummy-driver-id';

            const error = new PartnerResponseError({
                code: 'dhl:cz:get-shipment:error',
                message: GENERIC_GET_SHIPMENT_ERROR_MSG,
            });

            const httpClient = new FakeHTTPClient();
            const requestSpy = jest.spyOn(httpClient, 'request').mockResolvedValueOnce({
                status: 404,
                data: {
                    dataResponse: {
                        status: Status.ERROR,
                        sessionId: 'ce2934dd-b0a0-41e0-a98e-d1ddf8060e3d',
                        statusMessage: 'Shipment not found',
                        timestamp: '2019-10-18T14:04:05.185+02:0',
                        statusCode: '404',
                    },
                },
            });

            const client = new DHL_CZClientImpl({ httpClient, env });
            await expect(
                client.getShipment({
                    accessToken,
                    parcelIdentifier,
                    accessPointId,
                    operationType,
                    locale,
                    driverId,
                }),
            ).rejects.toThrow(error);

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/shipment`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: {
                    dataRequest: {
                        parcelIdentifier,
                        accessPointId,
                        operationType,
                        externalPartner: ExternalPartner,
                        businessType: BusinessType.PPL,
                        sessionId: expect.any(String),
                        applicationLocale: locale,
                        driverId,
                    },
                },
                /*
                    That'd be great to have a firm validation like validateStatus: () => true here,
                    but it seems that jest gets lost in the equality comparisons if we add this. So
                    we're sticking to a more generic `expect.any(Function)` for now.
                */
                validateStatus: expect.any(Function),
            });
        });
    });

    describe('associateLabel', () => {
        it('should perform a call to DHL CZ to notify a label association', async () => {
            const httpClient = new FakeHTTPClient();

            const dataResponse = {
                sessionId: 'dummy-session-id',
                statusMessage: 'dummy-status-message',
                status: Status.OK,
                timestamp: new Date().toUTCString(),
                statusCode: '200',
            };

            const requestSpy = jest
                .spyOn(httpClient, 'request')
                .mockResolvedValueOnce({ data: { dataResponse } });

            const dataElement = { event: { type: 'dummy' } } as unknown as DHLCZDataElement;
            const requestData = {
                externalPartner: 'Bloqit',
                businessType: BusinessType.PPL,
                accessPointId: 'dummy-access-point-id',
                driverId: '456',
                sessionId: 'dummy-session-id',
                applicationLocale: ApplicationLocale.cz,
            };

            const client = new DHL_CZClientImpl({ httpClient, env });
            const response = await client.associateLabel(
                { token: 'dummy-token', validity: new Date() },
                requestData,
                dataElement,
            );

            expect(requestSpy).toHaveBeenCalledTimes(1);
            expect(requestSpy).toHaveBeenCalledWith({
                method: 'post',
                url: `${env.DHL_CZ_API_HOST}/AccessPoint/v1/event`,
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer dummy-token`,
                    Connection: 'keep-alive',
                },
                httpsAgentOptions: { rejectUnauthorized: false },
                data: { dataElement, dataRequest: requestData },
            });

            expect(response).toEqual(dataResponse);
        });
    });
});
