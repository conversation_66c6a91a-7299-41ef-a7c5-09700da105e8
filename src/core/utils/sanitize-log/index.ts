
const SENSITIVE_KEYS = [
    /cookie/i,
    /api[-_]?key/i,
    /token/i,
    /auth[-_]?token/i,
    /x[-_]?api[-_]?key/i,
    /.*[-_]?secret/i,
    /.*[-_]?password/i,
]

export function sanitizeLog(input: unknown): unknown {
  if (typeof input === 'string') return sanitizeString(input)

  if (typeof input === 'object' && input !== null) {
    return sanitizeObject(input)
  }

  return input
}

function sanitizeString(str: string): string {
  for (const regex of SENSITIVE_KEYS) {
    str = str.replace(
      new RegExp(`("${regex.source}"\\s*:\\s*")[^"]+(")`, 'gi'),
      '$1***$2'
    )
  }
  return str
}

function sanitizeObject(obj: Record<string, any>): Record<string, any> {
  const clone: Record<string, any> = {}
  for (const key in obj) {
    if (SENSITIVE_KEYS.some((r) => r.test(key))) {
      clone[key] = '***'
    } else if (typeof obj[key] === 'object' && obj[key] !== null) {
      clone[key] = sanitizeLog(obj[key])
    } else {
      clone[key] = obj[key]
    }
  }
  return clone;
}